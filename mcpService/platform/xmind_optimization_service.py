#!/usr/bin/env python3
"""
XMind优化服务 - 提供XMind格式优化和转换功能
整合提示词优化和MD转XMind功能
"""
import os
import re
import logging
from typing import Dict, Any, List, Optional
from mcpService.common.base_service import BaseService
from mcpService.common.md2xmind import MD2XMind
from mcpService.platform.prompt_service import get_XMind格式优化提示词
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


class XMindOptimizationService(BaseService):
    """XMind优化服务类"""
    
    def __init__(self):
        """初始化XMind优化服务"""
        self.md2xmind_converter = MD2XMind()
        self.optimization_prompt = self._load_optimization_prompt()
    
    def _load_optimization_prompt(self) -> str:
        """加载XMind格式优化提示词"""
        try:
            result = get_XMind格式优化提示词()
            if result.get("status") == "success":
                return result["data"]["content"]
            else:
                logger.warning("无法加载XMind格式优化提示词，使用默认配置")
                return self._get_default_optimization_prompt()
        except Exception as e:
            logger.error(f"加载XMind格式优化提示词失败: {str(e)}")
            return self._get_default_optimization_prompt()
    
    def _get_default_optimization_prompt(self) -> str:
        """获取默认的优化提示词"""
        return """
请将测试用例内容转换为符合XMind导入标准的格式：
1. 使用#号表示层级结构
2. 每个用例作为独立分支
3. 标准格式：TL-用例标题、PD-直接写条件、步骤一、ER-直接写结果
4. 避免使用特殊字符：*、-、>
5. 步骤描述控制在15字以内
6. 确保层级结构清晰
"""
    
    def optimize_test_case_format(self, test_case_content: str) -> Dict[str, Any]:
        """
        优化测试用例格式，使其符合XMind标准
        :param test_case_content: 原始测试用例内容
        :return: 优化后的测试用例内容
        """
        try:
            # 这里可以集成AI服务来优化格式
            # 目前先进行基础的格式优化
            
            optimized_content = self._basic_format_optimization(test_case_content)
            
            return formatter.success(
                data={
                    "original_content": test_case_content,
                    "optimized_content": optimized_content,
                    "optimization_rules": [
                        "层级结构标准化",
                        "特殊字符清理",
                        "用例格式统一",
                        "编号规范统一"
                    ]
                },
                message="测试用例格式优化完成"
            )
            
        except Exception as e:
            logger.error(f"优化测试用例格式失败: {str(e)}")
            return formatter.error(
                message=f"优化测试用例格式失败: {str(e)}"
            )
    
    def _basic_format_optimization(self, content: str) -> str:
        """基础格式优化"""
        lines = content.split('\n')
        optimized_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 保留原始内容，只进行必要的格式处理
            # 避免破坏原有的测试用例结构
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _number_to_chinese(self, num: int) -> str:
        """数字转中文"""
        chinese_numbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
        if num <= 10:
            return chinese_numbers[num]
        elif num <= 20:
            return '十' + (chinese_numbers[num - 10] if num > 10 else '')
        else:
            return '二十' + (chinese_numbers[num - 20] if num > 20 else '')
    
    def create_xmind_from_content(self, 
                                content: str, 
                                output_path: str = None, 
                                title: str = None,
                                optimize_format: bool = True) -> Dict[str, Any]:
        """
        从内容创建XMind文件
        :param content: 测试用例内容
        :param output_path: 输出文件路径
        :param title: XMind标题
        :param optimize_format: 是否优化格式
        :return: 创建结果
        """
        try:
            # 参数校验
            if not content:
                return formatter.error(message="内容不能为空")
            
            # 如果需要优化格式
            if optimize_format:
                optimization_result = self.optimize_test_case_format(content)
                if optimization_result and optimization_result.get("status") == "success":
                    optimized_data = optimization_result.get("data", {})
                    if optimized_data:
                        content = optimized_data.get("optimized_content", content)
            
            # 创建临时markdown文件
            temp_md_path = "temp_test_cases.md"
            with open(temp_md_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 生成输出路径
            if output_path is None:
                output_path = "test_cases.xmind"
            
            # 设置默认标题
            if title is None:
                title = "测试用例"
            
            # 转换为XMind
            result = self.md2xmind_converter.convert_file(temp_md_path, output_path, title)
            
            # 清理临时文件
            if os.path.exists(temp_md_path):
                os.remove(temp_md_path)
            
            if result and result.get("status") == "success":
                return formatter.success(
                    data={
                        "file_path": result.get("file_path", output_path),
                        "title": title,
                        "format_optimized": optimize_format
                    },
                    message="XMind文件创建成功"
                )
            else:
                error_msg = result.get("message", "未知错误") if result else "转换失败"
                return formatter.error(
                    message=f"XMind文件创建失败: {error_msg}"
                )
                
        except Exception as e:
            logger.error(f"创建XMind文件失败: {str(e)}")
            return formatter.error(
                message=f"创建XMind文件失败: {str(e)}"
            )
    
    def batch_convert_markdown_to_xmind(self, 
                                      input_dir: str, 
                                      output_dir: str = None,
                                      optimize_format: bool = True) -> Dict[str, Any]:
        """
        批量转换markdown文件为XMind格式
        :param input_dir: 输入目录
        :param output_dir: 输出目录
        :param optimize_format: 是否优化格式
        :return: 批量转换结果
        """
        try:
            if not os.path.exists(input_dir):
                return formatter.error(
                    message=f"输入目录不存在: {input_dir}"
                )
            
            # 设置输出目录
            if output_dir is None:
                output_dir = input_dir
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 查找所有markdown文件
            md_files = []
            for root, dirs, files in os.walk(input_dir):
                for file in files:
                    if file.lower().endswith(('.md', '.markdown')):
                        md_files.append(os.path.join(root, file))
            
            if not md_files:
                return formatter.error(
                    message="未找到markdown文件"
                )
            
            # 转换结果统计
            conversion_results = []
            success_count = 0
            failure_count = 0
            
            for md_file in md_files:
                try:
                    # 读取文件内容
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 优化格式（如果需要）
                    if optimize_format:
                        optimization_result = self.optimize_test_case_format(content)
                        if optimization_result.get("status") == "success":
                            content = optimization_result["data"]["optimized_content"]
                    
                    # 计算输出路径
                    rel_path = os.path.relpath(md_file, input_dir)
                    base_name = os.path.splitext(rel_path)[0]
                    xmind_path = os.path.join(output_dir, f"{base_name}.xmind")
                    
                    # 确保输出目录存在
                    os.makedirs(os.path.dirname(xmind_path), exist_ok=True)
                    
                    # 转换为XMind
                    title = os.path.splitext(os.path.basename(md_file))[0]
                    result = self.md2xmind_converter.convert_file(md_file, xmind_path, title)
                    
                    if result.get("status") == "success":
                        success_count += 1
                        conversion_results.append({
                            "input_file": md_file,
                            "output_file": result["file_path"],
                            "status": "success"
                        })
                    else:
                        failure_count += 1
                        conversion_results.append({
                            "input_file": md_file,
                            "output_file": xmind_path,
                            "status": "failed",
                            "error": result.get("message", "未知错误")
                        })
                        
                except Exception as e:
                    failure_count += 1
                    conversion_results.append({
                        "input_file": md_file,
                        "status": "failed",
                        "error": str(e)
                    })
            
            return formatter.success(
                data={
                    "total_files": len(md_files),
                    "success_count": success_count,
                    "failure_count": failure_count,
                    "conversion_results": conversion_results,
                    "format_optimized": optimize_format
                },
                message=f"批量转换完成：成功 {success_count} 个，失败 {failure_count} 个"
            )
            
        except Exception as e:
            logger.error(f"批量转换失败: {str(e)}")
            return formatter.error(
                message=f"批量转换失败: {str(e)}"
            )
    
    def validate_xmind_format(self, content: str) -> Dict[str, Any]:
        """
        验证内容是否符合XMind格式标准
        :param content: 待验证的内容
        :return: 验证结果
        """
        try:
            validation_issues = []
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 检查特殊字符
                if re.search(r'[\*\-\>]', line):
                    validation_issues.append({
                        "line": i,
                        "content": line,
                        "issue": "包含特殊字符（*、-、>）",
                        "severity": "warning"
                    })
                
                # 检查层级结构
                if line.startswith('#'):
                    level = len(line) - len(line.lstrip('#'))
                    if level > 7:
                        validation_issues.append({
                            "line": i,
                            "content": line,
                            "issue": "层级过深（超过7层）",
                            "severity": "warning"
                        })
                
                # 检查用例格式
                if line.startswith('TL-') or line.startswith('MYTL-') or line.startswith('PATL-'):
                    # 检查是否包含必要的元素
                    has_pd = any('PD-' in l for l in lines[i:i+10])
                    has_step = any('步骤' in l for l in lines[i:i+10])
                    has_er = any('ER-' in l for l in lines[i:i+10])
                    
                    if not (has_pd and has_step and has_er):
                        validation_issues.append({
                            "line": i,
                            "content": line,
                            "issue": "用例缺少必要元素（前置条件、步骤或预期结果）",
                            "severity": "error"
                        })
            
            # 计算验证分数
            total_issues = len(validation_issues)
            error_count = len([issue for issue in validation_issues if issue["severity"] == "error"])
            warning_count = len([issue for issue in validation_issues if issue["severity"] == "warning"])
            
            score = max(0, 100 - (error_count * 10) - (warning_count * 2))
            
            return formatter.success(
                data={
                    "validation_score": score,
                    "total_issues": total_issues,
                    "error_count": error_count,
                    "warning_count": warning_count,
                    "validation_issues": validation_issues,
                    "format_valid": score >= 80
                },
                message=f"格式验证完成，得分：{score}/100"
            )
            
        except Exception as e:
            logger.error(f"验证XMind格式失败: {str(e)}")
            return formatter.error(
                message=f"验证XMind格式失败: {str(e)}"
            )
    
    def get_optimization_suggestions(self, content: str) -> Dict[str, Any]:
        """
        获取格式优化建议
        :param content: 待优化的内容
        :return: 优化建议
        """
        try:
            suggestions = []
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 检查特殊字符使用
                if '*' in line:
                    suggestions.append({
                        "line": i,
                        "content": line,
                        "suggestion": "建议移除*号，避免与XMind格式冲突",
                        "type": "format"
                    })
                
                # 检查步骤描述长度
                if line.startswith('步骤') and len(line) > 30:
                    suggestions.append({
                        "line": i,
                        "content": line,
                        "suggestion": "步骤描述过长，建议控制在15字以内",
                        "type": "readability"
                    })
                
                # 检查编号格式
                if re.match(r'^用例\d*[：:]', line):
                    suggestions.append({
                        "line": i,
                        "content": line,
                        "suggestion": "建议使用TL-前缀标准化用例编号",
                        "type": "standardization"
                    })
            
            return formatter.success(
                data={
                    "suggestions": suggestions,
                    "suggestion_count": len(suggestions),
                    "optimization_areas": list(set([s["type"] for s in suggestions]))
                },
                message=f"生成 {len(suggestions)} 条优化建议"
            )
            
        except Exception as e:
            logger.error(f"获取优化建议失败: {str(e)}")
            return formatter.error(
                message=f"获取优化建议失败: {str(e)}"
            )


# 全局XMind优化服务实例
xmind_optimization_service = XMindOptimizationService()


# 导出函数
def optimize_test_case_format(test_case_content: str) -> Dict[str, Any]:
    """优化测试用例格式"""
    return xmind_optimization_service.optimize_test_case_format(test_case_content)


def create_xmind_from_content(content: str, 
                            output_path: str = None, 
                            title: str = None,
                            optimize_format: bool = True) -> Dict[str, Any]:
    """从内容创建XMind文件"""
    return xmind_optimization_service.create_xmind_from_content(
        content, output_path, title, optimize_format
    )


def batch_convert_markdown_to_xmind(input_dir: str, 
                                 output_dir: str = None,
                                 optimize_format: bool = True) -> Dict[str, Any]:
    """批量转换markdown文件为XMind格式"""
    return xmind_optimization_service.batch_convert_markdown_to_xmind(
        input_dir, output_dir, optimize_format
    )


def validate_xmind_format(content: str) -> Dict[str, Any]:
    """验证XMind格式"""
    return xmind_optimization_service.validate_xmind_format(content)


def get_optimization_suggestions(content: str) -> Dict[str, Any]:
    """获取优化建议"""
    return xmind_optimization_service.get_optimization_suggestions(content)
