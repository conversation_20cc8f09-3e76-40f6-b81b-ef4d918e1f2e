import os
from mcpService.common.base_service import BaseService
from mcpService.common.md2xmind import MD2XMind  # Fallback to root import if not in common/
from mcpService.common.path_resolver import PathResolver

import logging
import re

logger = logging.getLogger(__name__)

class ConversionService(BaseService):
    @classmethod
    def convert_markdown_to_xmind(cls, markdown_path, output_path=None, title=None):
        """
        Convert a Markdown file to XMind format using md2xmind logic.
        :param markdown_path: Path to the input Markdown file.
        :param output_path: Optional output path for the XMind file.
        :param title: Title for the XMind root node.
        :return: Path to the generated XMind file or error message.
        """
        # 参数校验
        if not markdown_path:
            return {"status": "error", "message": "Markdown path cannot be empty"}
        
        try:
            # 使用路径解析器解析输入路径
            resolved_input_path = PathResolver.resolve_input_path(markdown_path)
            
            # 检查文件是否存在
            if not os.path.exists(resolved_input_path):
                return {"status": "error", "message": f"File {markdown_path} does not exist in any of the expected locations"}
            
            # 使用路径解析器解析输出路径
            resolved_output_path = PathResolver.resolve_output_path(
                output_path=output_path,
                input_path=resolved_input_path,
                filename=title
            )
            
            # 设置标题
            if title is None:
                title = os.path.splitext(os.path.basename(resolved_input_path))[0]
            
            logger.info(f"Converting {resolved_input_path} to {resolved_output_path} with title: {title}")
            logger.info(f"Environment: {'Server' if PathResolver.is_server_environment() else 'Local'}")
            
            # 执行转换
            converter = MD2XMind()
            converter.convert_file(resolved_input_path, resolved_output_path, title)
            
            # 验证输出文件是否创建成功
            if os.path.exists(resolved_output_path):
                return {"status": "success", "file_path": resolved_output_path}
            else:
                return {"status": "error", "message": "XMind file was not created successfully"}
                
        except Exception as e:
            logger.error(f"Conversion failed: {str(e)}")
            return {"status": "error", "message": f"Conversion failed: {str(e)}"}
