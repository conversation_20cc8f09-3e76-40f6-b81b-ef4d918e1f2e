# mcpService/platform/upload_service.py
import requests
from fastapi import HTTPException, UploadFile
import os
import tempfile
from mcpService.common.path_resolver import PathResolver
import logging

logger = logging.getLogger(__name__)


class UploadService:
    @classmethod
    def upload_xmind(cls, file_path: str, user_id: int, version_id: int):
        """
        上传XMind文件到测试用例平台
        支持文件路径字符串，便于MCP工具调用
        """
        url = f"http://test-case-platform-backend.testk8s.tsign.cn/tmsdefender/documentUpload?userId={user_id}&versionId={version_id}"
        headers = {
            "Referer": "http://test-case-platform-backend.testk8s.tsign.cn/",
            "User-Agent": "MoApifox/1.0.0 (https://apifox.com)",
        }

        try:
            # 使用路径解析器解析文件路径
            resolved_file_path = PathResolver.resolve_input_path(file_path)
            logger.info(f"上传文件路径解析: {file_path} -> {resolved_file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(resolved_file_path):
                return {
                    "code": 404,
                    "message": f"文件不存在: {resolved_file_path}",
                    "data": None
                }

            # 打开文件并上传
            with open(resolved_file_path, 'rb') as file:
                files = {"file": (os.path.basename(resolved_file_path), file, "application/vnd.xmind.workbook")}
                response = requests.post(url, headers=headers, files=files, verify=False)

            # 直接返回平台原始响应
            response_data = response.json()
            if response.status_code == 200:
                return {
                    "code": 0,
                    "message": "上传成功",
                    "data": response_data
                }
            else:
                return {
                    "code": response.status_code,
                    "message": f"上传失败: {response_data.get('message', '未知错误')}",
                    "data": None
                }
        except Exception as e:
            return {
                "code": 500,
                "message": f"请求异常: {str(e)}",
                "data": None
            }

    @classmethod
    def upload_xmind_from_uploadfile(cls, file: UploadFile, user_id: int, version_id: int):
        """
        上传XMind文件到测试用例平台 - 支持FastAPI UploadFile对象
        """
        url = f"http://test-case-platform-backend.testk8s.tsign.cn/tmsdefender/documentUpload?userId={user_id}&versionId={version_id}"
        headers = {
            "Referer": "http://test-case-platform-backend.testk8s.tsign.cn/",
            "User-Agent": "MoApifox/1.0.0 (https://apifox.com)",
        }

        try:
            files = {"file": (file.filename, file.file, "application/vnd.xmind.workbook")}
            response = requests.post(url, headers=headers, files=files, verify=False)

            # 直接返回平台原始响应
            response_data = response.json()
            if response.status_code == 200:
                return {
                    "code": 0,
                    "message": "上传成功",
                    "data": response_data
                }
            else:
                return {
                    "code": response.status_code,
                    "message": f"上传失败: {response_data.get('message', '未知错误')}",
                    "data": None
                }
        except Exception as e:
            return {
                "code": 500,
                "message": f"请求异常: {str(e)}",
                "data": None
            }
        finally:
            file.file.close()
