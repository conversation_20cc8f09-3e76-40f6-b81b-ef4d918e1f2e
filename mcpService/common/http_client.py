#!/usr/bin/env python3
"""
HTTP客户端 - 统一HTTP请求处理
支持环境切换、请求日志、错误处理
"""
import json
import httpx
import logging
from typing import Dict, Any, Optional
from app.core.config import *

logger = logging.getLogger(__name__)


class HttpClient:
    """HTTP客户端"""
    
    def __init__(self):
        self.timeout = REQUEST_TIMEOUT
        self.log_requests = True

    def get_api_config(self, environment: Optional[str] = None):
        """获取API配置"""
        current_env = environment or current_environment
        return get_api_config(current_env)
    
    async def make_request(
        self, 
        url: str, 
        method: str = "POST", 
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        environment: Optional[str] = None,
        domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            url: 请求地址
            method: 请求方法，默认POST
            data: 请求数据
            headers: 请求头
            params: URL参数
            environment: 环境描述
            domain: 业务域

        Returns:
            响应结果
        """
        try:
            # 处理环境
            if environment:
                detected_env = detect_environment_from_text(environment)

            # 获取API配置
            api_config = self.get_api_config(environment)

            # 构建默认请求头
            default_headers = {
                "Content-Type": "application/json",
                "X-Tsign-Open-Auth-Mode": "simple",
                "X-Tsign-Service-Group": "DEFAULT"
            }

            # 添加应用ID
            if "app_id" in api_config:
                default_headers["X-Tsign-Open-App-Id"] = api_config["app_id"]

            # 添加租户ID
            if X_TSIGN_OPEN_TENANT_ID:
                default_headers["X-Tsign-Open-Tenant-Id"] = X_TSIGN_OPEN_TENANT_ID

            # 合并请求头
            if headers:
                merged_headers = {**default_headers, **headers}
            else:
                merged_headers = default_headers
            
            # 记录请求信息
            if self.log_requests:
                logger.info(f"HTTP请求: {method} {url}")
                logger.info(f"请求头: {json.dumps(merged_headers, ensure_ascii=False)}")
                if data:
                    logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            
            # 发送请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(
                        url, 
                        headers=merged_headers,
                        params=params or data
                    )
                elif method.upper() == "POST":
                    response = await client.post(
                        url, 
                        headers=merged_headers,
                        json=data,
                        params=params
                    )
                elif method.upper() == "PUT":
                    response = await client.put(
                        url, 
                        headers=merged_headers,
                        json=data,
                        params=params
                    )
                elif method.upper() == "DELETE":
                    response = await client.delete(
                        url, 
                        headers=merged_headers,
                        json=data,
                        params=params
                    )
                else:
                    return {
                        "status": "error",
                        "message": f"不支持的请求方法: {method}"
                    }
            
            # 处理响应
            try:
                result = response.json()
            except ValueError:
                result = {"text": response.text}
            
            # 记录响应信息
            if self.log_requests:
                logger.info(f"HTTP响应: {response.status_code}")
                logger.info(f"响应数据: {json.dumps(result, ensure_ascii=False)}")
            
            # 添加请求和响应详情信息到结果中
            if isinstance(result, dict):
                result["request_details"] = {
                    "url": url,
                    "method": method,
                    "headers": merged_headers,
                    "data": data,
                    "params": params,
                    "environment": environment or "test",
                    "domain": domain
                }

                result["response_details"] = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "response_time": "N/A"  # 可以后续添加响应时间计算
                }
            else:
                # 如果result不是字典，包装成字典格式
                result = {
                    "data": result,
                    "request_details": {
                        "url": url,
                        "method": method,
                        "headers": merged_headers,
                        "data": data,
                        "params": params,
                        "environment": environment or "test",
                        "domain": domain
                    },
                    "response_details": {
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "response_time": "N/A"
                    }
                }
            
            return result
            
        except httpx.RequestError as e:
            logger.error(f"HTTP请求异常: {str(e)}")
            return {
                "status": "error",
                "message": f"HTTP请求异常: {str(e)}",
                "request_details": {
                    "url": url,
                    "method": method,
                    "headers": merged_headers if 'merged_headers' in locals() else {},
                    "data": data,
                    "params": params,
                    "environment": environment or "test",
                    "domain": domain
                }
            }
        except Exception as e:
            logger.error(f"请求处理异常: {str(e)}")
            return {
                "status": "error",
                "message": f"请求处理异常: {str(e)}",
                "request_details": {
                    "url": url,
                    "method": method,
                    "headers": merged_headers if 'merged_headers' in locals() else {},
                    "data": data,
                    "params": params,
                    "environment": environment or "test",
                    "domain": domain
                }
            }


# 全局HTTP客户端实例
http_client = HttpClient()
