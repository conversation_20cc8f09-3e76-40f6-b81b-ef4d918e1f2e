#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD2XMind - Markdown to XMind Converter
将Markdown文档转换为XMind思维导图

基于现代XMind文件格式（JSON）开发
版本: 2.0.0
"""

import os
import re
import sys
import json
import uuid
import time
import argparse
import zipfile
from typing import List, Dict, Any, Optional
from datetime import datetime
import base64


class MarkdownParser:
    """Markdown解析器 - 保持不变"""
    
    def __init__(self):
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.list_pattern = re.compile(r'^(\s*)([\-\*\+]|\d+\.)\s+(.+)$', re.MULTILINE)
        
    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """解析markdown文件，返回层次结构"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return self.parse_content(content)
    
    def parse_content(self, content: str) -> List[Dict[str, Any]]:
        """解析markdown内容"""
        lines = content.split('\n')
        structure = []
        current_stack = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            # 解析标题
            heading_match = self.heading_pattern.match(line)
            if heading_match:
                level = len(heading_match.group(1))
                title = heading_match.group(2).strip()
                
                node = {
                    'type': 'heading',
                    'level': level,
                    'title': title,
                    'children': [],
                    'line_number': line_num
                }
                
                # 调整堆栈到合适的层级
                while current_stack and current_stack[-1]['level'] >= level:
                    current_stack.pop()
                
                # 将节点添加到正确的父节点
                if current_stack:
                    current_stack[-1]['children'].append(node)
                else:
                    structure.append(node)
                
                current_stack.append(node)
            
            # 解析列表项
            elif line.startswith(('- ', '* ', '+ ')) or re.match(r'^\d+\.\s', line):
                list_match = self.list_pattern.match(line)
                if list_match:
                    indent = len(list_match.group(1))
                    marker = list_match.group(2)
                    text = list_match.group(3).strip()
                    
                    node = {
                        'type': 'list_item',
                        'level': indent // 2 + 1,
                        'title': text,
                        'children': [],
                        'line_number': line_num,
                        'marker': marker
                    }
                    
                    # 添加到当前标题下或作为独立项
                    if current_stack:
                        current_stack[-1]['children'].append(node)
                    else:
                        structure.append(node)
            
            # 处理普通文本段落
            elif line and not line.startswith('#'):
                if current_stack and len(line) < 100:
                    node = {
                        'type': 'text',
                        'level': current_stack[-1]['level'] + 1,
                        'title': line,
                        'children': [],
                        'line_number': line_num
                    }
                    current_stack[-1]['children'].append(node)
        
        return structure


class XMindConverter:
    """现代XMind转换器 - 生成JSON格式"""
    
    def __init__(self):
        self.sheet_id = self._generate_id()
        self.root_topic_id = self._generate_id()
        self.timestamp = int(time.time() * 1000)
        
    def _generate_id(self) -> str:
        """生成XMind格式的ID"""
        return str(uuid.uuid4()).replace('-', '')[:26]
    
    def _create_topic(self, node: Dict[str, Any], parent_id: str = None) -> Dict[str, Any]:
        """创建主题节点（JSON格式）"""
        topic_id = self._generate_id()
        
        topic = {
            "id": topic_id,
            "title": node['title']
        }
        
        # 如果有子节点，递归创建
        if node.get('children'):
            children = []
            for child_node in node['children']:
                child_topic = self._create_topic(child_node, topic_id)
                children.append(child_topic)
            
            if children:
                topic["children"] = {
                    "attached": children
                }
        
        return topic
    
    def create_xmind_content(self, structure: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
        """创建XMind内容（JSON格式）"""
        
        # 创建根主题
        root_topic = {
            "id": self.root_topic_id,
            "title": title
        }
        
        # 添加子主题
        if structure:
            children = []
            for node in structure:
                child_topic = self._create_topic(node, self.root_topic_id)
                children.append(child_topic)
            
            root_topic["children"] = {
                "attached": children
            }
        
        # 创建完整的XMind JSON结构
        xmind_data = [{
            "id": self.sheet_id,
            "class": "sheet",
            "title": "画布 1",
            "rootTopic": root_topic
        }]
        
        return xmind_data
    
    def create_manifest(self) -> Dict[str, Any]:
        """创建manifest.json"""
        return {
            "file-entries": {
                "content.json": {},
                "metadata.json": {},
                "Thumbnails/thumbnail.png": {}
            }
        }
    
    def create_metadata(self) -> Dict[str, Any]:
        """创建metadata.json"""
        return {
            "creator": {
                "name": "MD2XMind",
                "version": "2.0.0"
            },
            "activeSheetId": self.sheet_id
        }
    
    def create_thumbnail(self) -> bytes:
        """创建简单的缩略图PNG（1x1像素的透明图片）"""
        # 最小的PNG文件 - 1x1透明像素
        png_data = base64.b64decode(
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jIN8OQAAAABJRU5ErkJggg=="
        )
        return png_data
    
    def save_xmind(self, xmind_data: List[Dict[str, Any]], output_path: str):
        """保存为XMind文件"""
        
        # 创建临时文件
        manifest = self.create_manifest()
        metadata = self.create_metadata()
        thumbnail = self.create_thumbnail()
        
        # 创建ZIP文件
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加content.json
            zipf.writestr('content.json', json.dumps(xmind_data, ensure_ascii=False, separators=(',', ':')))
            
            # 添加manifest.json
            zipf.writestr('manifest.json', json.dumps(manifest, separators=(',', ':')))
            
            # 添加metadata.json
            zipf.writestr('metadata.json', json.dumps(metadata, separators=(',', ':')))
            
            # 创建Thumbnails目录并添加缩略图
            zipf.writestr('Thumbnails/thumbnail.png', thumbnail)


class MD2XMind:
    """主要的转换类"""
    
    def __init__(self):
        self.parser = MarkdownParser()
        self.converter = XMindConverter()
    
    def convert_file(self, input_path: str, output_path: str = None, title: str = None):
        """转换单个markdown文件"""
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        # 生成输出路径
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = f"{base_name}.xmind"
        
        # 生成标题
        if title is None:
            title = os.path.splitext(os.path.basename(input_path))[0]
        
        print(f"正在转换: {input_path} -> {output_path}")
        
        # 解析markdown
        structure = self.parser.parse_file(input_path)
        
        if not structure:
            print("警告: 没有找到可转换的内容")
            return
        
        # 创建XMind内容
        xmind_data = self.converter.create_xmind_content(structure, title)
        
        # 保存文件
        self.converter.save_xmind(xmind_data, output_path)
        print(f"转换完成: {output_path}")
    
    def convert_directory(self, input_dir: str, output_dir: str = None):
        """批量转换目录中的所有markdown文件"""
        if output_dir is None:
            output_dir = input_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        md_files = []
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                if file.lower().endswith(('.md', '.markdown')):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            print("没有找到markdown文件")
            return
        
        print(f"找到 {len(md_files)} 个markdown文件")
        
        for md_file in md_files:
            try:
                # 计算相对路径
                rel_path = os.path.relpath(md_file, input_dir)
                base_name = os.path.splitext(rel_path)[0]
                output_path = os.path.join(output_dir, f"{base_name}.xmind")
                
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                self.convert_file(md_file, output_path)
                
            except Exception as e:
                print(f"转换失败 {md_file}: {str(e)}")


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(
        description="将Markdown文档转换为XMind思维导图 (现代JSON格式)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 转换单个文件
  python md2xmind.py input.md
  
  # 指定输出文件名
  python md2xmind.py input.md -o output.xmind
  
  # 批量转换目录
  python md2xmind.py -d ./docs/ -od ./xmind_output/
  
  # 指定思维导图标题
  python md2xmind.py input.md -t "我的思维导图"
        """
    )
    
    parser.add_argument('input', nargs='?', help='输入的markdown文件路径')
    parser.add_argument('-o', '--output', help='输出的xmind文件路径')
    parser.add_argument('-t', '--title', help='思维导图的标题')
    parser.add_argument('-d', '--directory', help='输入目录路径（批量转换）')
    parser.add_argument('-od', '--output-directory', help='输出目录路径（批量转换）')
    
    args = parser.parse_args()
    
    # 检查参数
    if not args.input and not args.directory:
        parser.print_help()
        return
    
    try:
        converter = MD2XMind()
        
        if args.directory:
            # 批量转换模式
            converter.convert_directory(args.directory, args.output_directory)
        else:
            # 单文件转换模式
            converter.convert_file(args.input, args.output, args.title)
            
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main() 