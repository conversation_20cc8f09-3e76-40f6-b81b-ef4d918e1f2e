from dataclasses import dataclass
from typing import Protocol


# 协议定义
class OpenApi(Protocol):

    @property
    def open_api_url(self) -> str: ...

    @property
    def default_app_id(self) -> str: ...

    @property
    def beike_app_id(self) -> str: ...

    @property
    def bzq_app_id(self) -> str: ...

    @property
    def default_phone(self) -> str: ...

    @property
    def dedicated_id(self) -> str: ...


# 配置封装
@dataclass(frozen=True)
class OpenApiConfig:
    open_api_url: str
    default_app_id: str
    beike_app_id: str
    bzq_app_id: str
    default_phone: str
    default_org_name: str
    dedicated_id: str
    default_file_id: str


# 预定义环境配置
TEST_CONFIG = OpenApiConfig(
    open_api_url="http://in-testopenapi.tsign.cn",
    default_app_id="7876611670",
    beike_app_id="7876625192",
    bzq_app_id="3438757422",
    default_phone="16111111111",
    default_org_name="esigntest李保辉能力有限公司",
    dedicated_id="32884883",
    default_file_id="984bcad0666c4d329658586f86e1e9f0"
)

SML_CONFIG = OpenApiConfig(
    open_api_url="http://in-sml-ws-openapi.tsign.cn",
    default_app_id="4438869840",
    beike_app_id="4438879859",
    bzq_app_id="4438758872",
    default_phone="16111111111",
    default_org_name="esigntest李保辉能力有限公司",
    dedicated_id="1554960001",
    default_file_id="267aea41bf494df4a52498cc1ce41a43"
)


class GenericOpenApi:
    def __init__(self, config: OpenApiConfig):
        self._config = config

    @property
    def open_api_url(self) -> str:
        return self._config.open_api_url

    @property
    def default_app_id(self) -> str:
        return self._config.default_app_id

    @property
    def beike_app_id(self) -> str:
        return self._config.beike_app_id

    @property
    def bzq_app_id(self) -> str:
        return self._config.bzq_app_id

    @property
    def default_phone(self) -> str:
        return self._config.default_phone

    @property
    def default_org_name(self) -> str:
        return self._config.default_org_name

    @property
    def dedicated_id(self) -> str:
        return self._config.dedicated_id

    @property
    def default_file_id(self) -> str:
        return self._config.default_file_id


# 工厂模式创建实例
class OpenApiFactory:
    _configs = {"test": TEST_CONFIG, "sml": SML_CONFIG}

    @classmethod
    def create(cls, env: str) -> GenericOpenApi:
        if env.lower() == "moni" or env.__contains__("模拟") or env.lower().__contains__("sml"):
            env = "sml"
        else:
            env = "test"
        return GenericOpenApi(cls._configs[env])


if __name__ == '__main__':
    test_api = OpenApiFactory.create("SML")
    print(test_api.open_api_url)
