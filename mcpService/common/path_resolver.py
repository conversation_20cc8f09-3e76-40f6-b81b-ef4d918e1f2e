import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class PathResolver:
    """路径解析器 - 处理本地和服务器环境的路径差异"""
    
    # 服务器环境的基础目录
    SERVER_BASE_DIR = "/app"
    
    @classmethod
    def resolve_input_path(cls, file_path: str) -> str:
        """
        解析输入文件路径
        :param file_path: 用户提供的文件路径
        :return: 解析后的实际文件路径
        """
        if not file_path:
            raise ValueError("文件路径不能为空")
        
        # 标准化路径
        file_path = os.path.normpath(file_path)
        
        # 如果已经是绝对路径且存在，直接返回
        if os.path.isabs(file_path) and os.path.exists(file_path):
            return file_path
        
        # 获取当前工作目录
        current_dir = os.getcwd()
        
        # 检查是否在服务器环境
        is_server_env = os.path.exists(cls.SERVER_BASE_DIR)
        
        # 尝试多种可能的路径
        possible_paths = []
        
        if is_server_env:
            # 服务器环境：优先在 /app 目录下查找
            possible_paths.extend([
                os.path.join(cls.SERVER_BASE_DIR, file_path),
                os.path.join(cls.SERVER_BASE_DIR, "data", file_path),
                os.path.join(cls.SERVER_BASE_DIR, "docs", file_path),
                os.path.join(cls.SERVER_BASE_DIR, "temp", file_path),
            ])
        else:
            # 本地环境：在当前目录和常见目录下查找
            possible_paths.extend([
                file_path,
                os.path.join(current_dir, file_path),
                os.path.join(current_dir, "data", file_path),
                os.path.join(current_dir, "docs", file_path),
                os.path.join(current_dir, "temp", file_path),
            ])
        
        # 尝试找到存在的文件
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"找到文件: {path}")
                return path
        
        # 如果都没找到，返回原始路径（让后续处理报错）
        logger.warning(f"未找到文件: {file_path}，尝试的路径: {possible_paths}")
        return file_path
    
    @classmethod
    def resolve_output_path(cls, output_path: Optional[str] = None, 
                          input_path: Optional[str] = None, 
                          filename: Optional[str] = None) -> str:
        """
        解析输出文件路径
        :param output_path: 用户指定的输出路径
        :param input_path: 输入文件路径（用于生成默认输出路径）
        :param filename: 指定的文件名
        :return: 解析后的输出路径
        """
        # 检查是否在服务器环境
        is_server_env = os.path.exists(cls.SERVER_BASE_DIR)
        
        # 确定基础输出目录
        if is_server_env:
            base_output_dir = cls.SERVER_BASE_DIR
        else:
            base_output_dir = os.getcwd()
        
        # 创建输出目录（如果不存在）
        os.makedirs(base_output_dir, exist_ok=True)
        
        # 如果用户指定了输出路径
        if output_path:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(output_path):
                output_path = os.path.join(base_output_dir, output_path)
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            return output_path
        
        # 如果没有指定输出路径，根据输入路径生成
        if input_path:
            # 获取输入文件名（不含扩展名）
            input_filename = os.path.splitext(os.path.basename(input_path))[0]
            
            # 生成输出文件名
            if filename:
                # 如果指定了文件名，使用指定的文件名
                output_filename = f"{filename}.xmind"
            else:
                # 否则使用输入文件名
                output_filename = f"{input_filename}.xmind"
            
            # 构建输出路径
            output_path = os.path.join(base_output_dir, output_filename)
        else:
            # 如果没有输入路径，使用默认文件名
            default_filename = filename or "converted_output"
            output_path = os.path.join(base_output_dir, f"{default_filename}.xmind")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"输出路径: {output_path}")
        return output_path
    
    @classmethod
    def get_server_base_dir(cls) -> str:
        """获取服务器基础目录"""
        return cls.SERVER_BASE_DIR
    
    @classmethod
    def is_server_environment(cls) -> bool:
        """检查是否在服务器环境"""
        return os.path.exists(cls.SERVER_BASE_DIR)
    
    @classmethod
    def resolve_for_mcp(cls, file_path: str) -> str:
        """
        专门为MCP工具解析文件路径
        解决MCP工具无法正确解析相对路径的问题
        :param file_path: 用户提供的文件路径
        :return: 解析后的绝对路径
        """
        if not file_path:
            raise ValueError("文件路径不能为空")
        
        # 标准化路径
        file_path = os.path.normpath(file_path)
        
        # 如果已经是绝对路径且存在，直接返回
        if os.path.isabs(file_path) and os.path.exists(file_path):
            return file_path
        
        # 获取当前工作目录
        current_dir = os.getcwd()
        
        # 尝试多种可能的路径，优先级从高到低
        possible_paths = []
        
        # 1. 尝试直接使用当前路径
        possible_paths.append(file_path)
        
        # 2. 尝试在当前工作目录下查找
        possible_paths.append(os.path.join(current_dir, file_path))
        
        # 3. 尝试在docs目录下查找
        possible_paths.append(os.path.join(current_dir, "docs", os.path.basename(file_path)))
        
        # 4. 尝试在data目录下查找
        possible_paths.append(os.path.join(current_dir, "data", os.path.basename(file_path)))
        
        # 5. 尝试在temp目录下查找
        possible_paths.append(os.path.join(current_dir, "temp", os.path.basename(file_path)))
        
        # 6. 如果在服务器环境，尝试在服务器目录下查找
        if os.path.exists(cls.SERVER_BASE_DIR):
            possible_paths.extend([
                os.path.join(cls.SERVER_BASE_DIR, file_path),
                os.path.join(cls.SERVER_BASE_DIR, "docs", os.path.basename(file_path)),
                os.path.join(cls.SERVER_BASE_DIR, "data", os.path.basename(file_path)),
                os.path.join(cls.SERVER_BASE_DIR, "temp", os.path.basename(file_path)),
            ])
        
        # 尝试找到存在的文件
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"MCP路径解析成功: {path}")
                return path
        
        # 如果都没找到，记录详细错误信息
        logger.error(f"MCP路径解析失败: {file_path}")
        logger.error(f"尝试的路径: {possible_paths}")
        
        # 返回第一个可能的路径（让后续处理报错）
        return possible_paths[0] if possible_paths else file_path
