#!/usr/bin/env python3
"""
文件服务常量定义
"""


# 签署流程默认值 - 第一套（原始默认值）
class SignFlowDefaults:
    # 附件配置
    ATTACHMENT_FILE_ID = "fbc79bff6af14517a3bb2c496594c602"

    # 抄送人配置
    COPIER_PSN_ACCOUNT = "***********"
    COPIER_PSN_ID = "e79a670bb92d4be893a7c1d1d71ab3e6"

    # 文档配置
    DOC_FILE_ID = "267aea41bf494df4a52498cc1ce41a43"

    # 签署流程配置
    AUDIO_VIDEO_TEMPLATE_ID = ""
    GLOBAL_ACCESS_CODE = ""
    GLOBAL_AUTH_MODES = "NO_NEED"
    GLOBAL_WILLINGNESS = True
    ORG_AVAILABLE_AUTH_MODES = "ORG_LEGALREP"
    PSN_AVAILABLE_AUTH_MODES = ""
    UNEDITABLE_FIELDS = "orgIDCardNum"
    WILLINGNESS_AUTH_MODES = "CODE_SMS"
    AUTO_FINISH = True
    AUTO_START = True
    BARRIER_CODE = ""
    CHARGE_MODE = 0
    ORDER_TYPE = ""
    ALLOW_TO_RESCIND = True
    CONTRACT_SECRECY = 1
    IDENTITY_VERIFY = False
    EXAMINE_NOTICE = True
    NOTICE_TYPES = "1,2,3,4,5,6,7"
    NOTIFY_URL = "https://libaohui.com.cn/callback/ding"
    REDIRECT_DELAY_TIME = 3
    REDIRECT_URL = "https://www.esign.cn"
    AVAILABLE_SIGN_CLIENT_TYPES = "1,2,3"
    DEDICATED_CLOUD_ID = "${dedicatedCloudId}"
    FDA_LANGUAGE_MODE = "ENGLISH"
    FDA_SIGNING_REASON = "一去二三里烟村四五家亭台六七座,八九十枝花危楼高百尺手可摘星辰,不敢高声语恐惊天上人离离原上草,一岁一枯荣野火烧不尽春风吹又生,远芳侵古道晴翠接芳城又送王孙去"
    SHOW_BATCH_DROP_SEAL_BUTTON = True
    SIGN_MODE = "NORMAL"
    SIGN_TIPS_CONTENT = ""
    SIGN_TIPS_TITLE = ""
    SIGN_TYPE = "FDAType"
    SIGN_FLOW_TITLE = "模拟环境mcp发起"

    # 发起方配置
    ORG_ID = "9dfd1878c4854bb787e0f6818f52768e"
    TRANSACTOR_PSN_ID = "105dd675b9a64f5d85ddd325f82a1c4e"
    PSN_INITIATOR_ID = "bf565cbf5a96446f9fc76523dc2342c4"

    # 签署方配置
    SIGNER_AUDIO_VIDEO_TEMPLATE_ID = ""
    SIGNER_GLOBAL_ACCESS_CODE = ""
    SIGNER_GLOBAL_AUTH_MODES = "NO_NEED"
    SIGNER_GLOBAL_WILLINGNESS = True
    SIGNER_ORG_AVAILABLE_AUTH_MODES = "ORG_BANK_TRANSFER,ORG_LEGALREP"
    SIGNER_PSN_AVAILABLE_AUTH_MODES = "PSN_BANKCARD4"
    SIGNER_REPEATABLE_REAL_NAME = False
    SIGNER_UNEDITABLE_FIELDS = "orgIDCardNum"
    SIGNER_WILLINGNESS_AUTH_MODES = "SIGN_PWD,CODE_SMS,PSN_AUDIO_VIDEO_ALIPAY"
    SIGNER_EXAMINE_NOTICE = True
    SIGNER_NOTICE_TYPES = "1"
    SIGNER_ORG_ID = "6a3955f514734548bfdc051522cd3fd0"
    LEGAL_REP_ID_CARD_NUM = "130706199801071324"
    LEGAL_REP_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    LEGAL_REP_NAME = "测试米姣仪"
    ORG_ID_CARD_NUM = "91071161670240505d"
    ORG_ID_CARD_TYPE = "CRED_ORG_USCC"
    ORG_NAME = "esigntest李保辉能力有限公司"
    TRANSACTOR_PSN_ACCOUNT = "***********"
    TRANSACTOR_BANK_CARD_NUM = "623593507678536695"
    TRANSACTOR_PSN_ID_CARD_NUM = "130706199801071324"
    TRANSACTOR_PSN_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    TRANSACTOR_PSN_MOBILE = "***********"
    TRANSACTOR_PSN_NAME = "测试宣刚义"
    PSN_SIGNER_ACCOUNT = "***********"
    PSN_SIGNER_ID = "105dd675b9a64f5d85ddd325f82a1c4e"
    PSN_SIGNER_BANK_CARD_NUM = "623593507678536695"
    PSN_SIGNER_ID_CARD_NUM = "130706199801071324"
    PSN_SIGNER_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    PSN_SIGNER_MOBILE = "***********"
    PSN_SIGNER_NAME = "测试米姣仪"
    FORCED_READING_TIME = 0
    SIGN_ORDER = 1
    SIGN_TASK_TYPE = 0
    SIGN_TIPS_CONTENT_DETAIL = "1、你的辛苦毫无价值；\n2、你的加班无人认可；\n3、你的状态心力交瘁。\n你一切的努力只是上位者的垫脚石。"
    SIGN_TIPS_FILE_ID = "96a0eeef635c4331bbd61c49bf5f291a"
    SIGN_TIPS_TITLE_DETAIL = "牛马声明"
    UPLOAD_FILES_REQUIRED = True
    UPLOAD_DESCRIPTION = "身份证信息面"

    # 签署字段配置
    CUSTOM_BIZ_NUM = ""
    AUTO_SIGN = False
    DATE_FORMAT = ""
    FONT_SIZE = 20
    SIGN_DATE_POSITION_PAGE = 1
    SIGN_DATE_POSITION_X = 0
    SIGN_DATE_POSITION_Y = 0
    SIGN_FIELD_FILE_ID = "267aea41bf494df4a52498cc1ce41a43"
    MUST_SIGN = False
    ADAPTABLE_SIGN_FIELD_SIZE = True
    ASSIGNED_SEAL_ID = "ad2118c1-b2db-4201-bb28-206321c85ff4"
    AVAILABLE_SEAL_IDS = "2fd74896-2200-422a-b50c-bdcc4014b388"
    FREE_MODE = False
    MOVABLE_SIGN_FIELD = True
    ORG_SEAL_BIZ_TYPES = "ALL"
    PSN_SEAL_STYLES = ""
    SIGN_FIELD_HEIGHT = 200
    ACROSS_PAGE_MODE = "ALL"
    ACROSS_PAGE_OFFSET = 0
    POSITION_PAGE = "1"
    POSITION_X = 200
    POSITION_Y = 600
    SIGN_FIELD_SIZE = 0
    SIGN_FIELD_STYLE = 1
    SIGN_FIELD_WIDTH = 600
    AI_CHECK = 0
    REMARK_FREE_MODE = True
    INPUT_TYPE = 1
    REMARK_MOVABLE_SIGN_FIELD = True
    REMARK_CONTENT = "一二三"
    REMARK_FONT_SIZE = 20
    REMARK_SIGN_FIELD_HEIGHT = 100
    REMARK_SIGN_FIELD_WIDTH = 100
    SIGN_DATE_FONT_SIZE = 15
    SHOW_SIGN_DATE = 1
    SIGN_DATE_POSITION_X_DETAIL = 200
    SIGN_DATE_POSITION_Y_DETAIL = 400
    SIGN_FIELD_TYPE = 0


# 签署流程默认值 - 第二套（测试环境专用）
class SignFlowTestDefaults:
    # 附件配置
    ATTACHMENT_FILE_ID = "38bda84c826d4e2bb1053d83bd6f5473"

    # 抄送人配置
    COPIER_PSN_ACCOUNT = "***********"
    COPIER_PSN_ID = "9ba3fb745f404fadbdce9d4c83ea9e9f"

    # 文档配置
    DOC_FILE_ID = "984bcad0666c4d329658586f86e1e9f0"

    # 签署流程配置
    AUDIO_VIDEO_TEMPLATE_ID = ""
    GLOBAL_ACCESS_CODE = ""
    GLOBAL_AUTH_MODES = "NO_NEED"
    GLOBAL_WILLINGNESS = True
    ORG_AVAILABLE_AUTH_MODES = "ORG_LEGALREP"
    PSN_AVAILABLE_AUTH_MODES = ""
    UNEDITABLE_FIELDS = "orgIDCardNum"
    WILLINGNESS_AUTH_MODES = "CODE_SMS"
    AUTO_FINISH = True
    AUTO_START = True
    BARRIER_CODE = ""
    CHARGE_MODE = 0
    ORDER_TYPE = ""
    ALLOW_TO_RESCIND = True
    CONTRACT_SECRECY = 1
    IDENTITY_VERIFY = False
    EXAMINE_NOTICE = True
    NOTICE_TYPES = "1,2,3,4,5,6,7"
    NOTIFY_URL = "https://libaohui.com.cn/callback/ding"
    REDIRECT_DELAY_TIME = 3
    REDIRECT_URL = "https://www.esign.cn"
    AVAILABLE_SIGN_CLIENT_TYPES = "1,2,3"
    DEDICATED_CLOUD_ID = "********"
    FDA_LANGUAGE_MODE = "ENGLISH"
    FDA_SIGNING_REASON = "一去二三里烟村四五家亭台六七座,八九十枝花危楼高百尺手可摘星辰,不敢高声语恐惊天上人离离原上草,一岁一枯荣野火烧不尽春风吹又生,远芳侵古道晴翠接芳城又送王孙去"
    SHOW_BATCH_DROP_SEAL_BUTTON = True
    SIGN_MODE = "NORMAL"
    SIGN_TIPS_CONTENT = ""
    SIGN_TIPS_TITLE = ""
    SIGN_TYPE = "FDAType"
    SIGN_FLOW_EXPIRE_TIME = "*************"
    SIGN_FLOW_TITLE = "测试环境mcp发起"

    # 发起方配置
    ORG_ID = "5d9466b12a69455096926945b0c016a2"
    TRANSACTOR_PSN_ID = "6300761d28b6447b98eea4589db35a18"
    PSN_INITIATOR_ID = "5dbcbe27dad84b2d8f20d523a339ff33"

    # 签署方配置
    SIGNER_AUDIO_VIDEO_TEMPLATE_ID = ""
    SIGNER_GLOBAL_ACCESS_CODE = ""
    SIGNER_GLOBAL_AUTH_MODES = "NO_NEED"
    SIGNER_GLOBAL_WILLINGNESS = True
    SIGNER_ORG_AVAILABLE_AUTH_MODES = "ORG_BANK_TRANSFER,ORG_LEGALREP"
    SIGNER_PSN_AVAILABLE_AUTH_MODES = "PSN_BANKCARD4"
    SIGNER_REPEATABLE_REAL_NAME = False
    SIGNER_UNEDITABLE_FIELDS = "orgIDCardNum"
    SIGNER_WILLINGNESS_AUTH_MODES = "SIGN_PWD,CODE_SMS,PSN_AUDIO_VIDEO_ALIPAY"
    SIGNER_EXAMINE_NOTICE = True
    SIGNER_NOTICE_TYPES = "1"
    SIGNER_ORG_ID = "ea0647b7b2644642b8bdeb4a97ca3dda"
    LEGAL_REP_ID_CARD_NUM = "360122200102215195"
    LEGAL_REP_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    LEGAL_REP_NAME = "测试姬和"
    ORG_ID_CARD_NUM = "91027201647582405d"
    ORG_ID_CARD_TYPE = "CRED_ORG_USCC"
    ORG_NAME = "esigntest李保辉能力有限公司"
    TRANSACTOR_BANK_CARD_NUM = "6236520493045607043"
    TRANSACTOR_PSN_ID_CARD_NUM = "360122200102215195"
    TRANSACTOR_PSN_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    TRANSACTOR_PSN_MOBILE = "***********"
    TRANSACTOR_PSN_NAME = "测试宣刚义"
    PSN_SIGNER_ID = "6300761d28b6447b98eea4589db35a18"
    PSN_SIGNER_BANK_CARD_NUM = "6236520493045607043"
    PSN_SIGNER_ID_CARD_NUM = "360122200102215195"
    PSN_SIGNER_ID_CARD_TYPE = "CRED_PSN_CH_IDCARD"
    PSN_SIGNER_MOBILE = "***********"
    PSN_SIGNER_NAME = "测试姬和"
    FORCED_READING_TIME = 0
    SIGN_ORDER = 1
    SIGN_TASK_TYPE = 0
    SIGN_TIPS_CONTENT_DETAIL = "1、你的辛苦毫无价值；\n2、你的加班无人认可；\n3、你的状态心力交瘁。\n你一切的努力只是上位者的垫脚石。"
    SIGN_TIPS_FILE_ID = "faa68ff796c44e30a17ea87a6343dbe2"
    SIGN_TIPS_TITLE_DETAIL = "牛马声明"
    UPLOAD_FILES_REQUIRED = True
    UPLOAD_DESCRIPTION = "身份证信息面"

    # 签署字段配置
    CUSTOM_BIZ_NUM = ""
    AUTO_SIGN = False
    DATE_FORMAT = ""
    FONT_SIZE = 20
    SIGN_DATE_POSITION_PAGE = 1
    SIGN_DATE_POSITION_X = 0
    SIGN_DATE_POSITION_Y = 0
    SIGN_FIELD_FILE_ID = "984bcad0666c4d329658586f86e1e9f0"
    MUST_SIGN = False
    ADAPTABLE_SIGN_FIELD_SIZE = True
    ASSIGNED_SEAL_ID = "ad2118c1-b2db-4201-bb28-206321c85ff4"
    AVAILABLE_SEAL_IDS = "2fd74896-2200-422a-b50c-bdcc4014b388"
    FREE_MODE = False
    MOVABLE_SIGN_FIELD = True
    ORG_SEAL_BIZ_TYPES = "ALL"
    PSN_SEAL_STYLES = ""
    SIGN_FIELD_HEIGHT = 200
    ACROSS_PAGE_MODE = "ALL"
    ACROSS_PAGE_OFFSET = 0
    POSITION_PAGE = "1"
    POSITION_X = 200
    POSITION_Y = 600
    SIGN_FIELD_SIZE = 0
    SIGN_FIELD_STYLE = 1
    SIGN_FIELD_WIDTH = 600
    AI_CHECK = 0
    REMARK_FREE_MODE = True
    INPUT_TYPE = 1
    REMARK_MOVABLE_SIGN_FIELD = True
    REMARK_CONTENT = "一二三"
    REMARK_FONT_SIZE = 20
    REMARK_SIGN_FIELD_HEIGHT = 100
    REMARK_SIGN_FIELD_WIDTH = 100
    SIGN_DATE_FONT_SIZE = 15
    SHOW_SIGN_DATE = 1
    SIGN_DATE_POSITION_X_DETAIL = 200
    SIGN_DATE_POSITION_Y_DETAIL = 400
    SIGN_FIELD_TYPE = 0


# 根据环境获取对应的默认值类
def get_defaults_by_env(env: str):
    """
    根据环境参数获取对应的默认值类

    Args:
        env: 环境类型，如"测试环境"，"模拟环境"

    Returns:
        对应的默认值类
    """
    if env == "测试环境":
        return SignFlowTestDefaults
    else:
        # 其他环境使用原始默认值
        return SignFlowDefaults
