"""
Wiki登录服务
处理Wiki系统的登录认证和Cookie管理
"""
import os
import uuid
import logging
from typing import Optional, List
from pathlib import Path

import httpx
from bs4 import BeautifulSoup

from app.core.config import get_wiki_config

logger = logging.getLogger(__name__)

class WikiLoginService:
    """Wiki登录服务"""
    
    def __init__(self):
        self.config = get_wiki_config()
        self._client: Optional[httpx.AsyncClient] = None
        self._cookies: Optional[dict] = None
        
    async def get_cookie(self) -> str:
        """获取登录Cookie"""
        if self._cookies:
            return self._cookies
        # 如果没有配置Cookie，执行登录
        return await self._login()
        
    async def _login(self) -> str:
        """执行登录操作"""
        if not self.config["username"] or not self.config["password"]:
            raise ValueError("Wiki用户名和密码未配置")
            
        logger.info("开始Wiki登录...")
        
        # 创建HTTP客户端，启用重定向跟踪
        async with httpx.AsyncClient(
            follow_redirects=True,
            verify=False,
            timeout=30.0
        ) as client:
            # 构建登录表单
            form_data = {
                "username": self.config["username"],
                "password": self.config["password"]
            }
            
            # 构建请求头
            headers = {
                "host": "zerotrust.esign.cn",
                "origin": "https://zerotrust.esign.cn",
                "pragma": "no-cache",
                "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "referer": "https://zerotrust.esign.cn/zerotrustQR/?_redirect=http%3A%2F%2Fwiki.timevale.cn%3A8081%2Fpages%2Fviewpage.action%3FpageId%3D190221799%26zToken%3D04e1481b-f03d-42a0-b7bd-ba4965845ba2",
                "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "cors",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1"
            }
            
            try:
                # 发送登录请求
                response = await client.post(
                    self.config["login_url"],
                    data=form_data,
                    headers=headers
                )
                
                logger.info(f"登录响应状态码: {response.status_code}")
                logger.info(f"登录响应URL: {response.url}")

                # 获取所有cookie，包括重定向过程中的
                all_cookies = []
                for r in response.history + [response]:
                    all_cookies.append(r.cookies)

                cookies_dict = {}
                access_token = client.cookies.get("access_token")

                # 提取多个 middle_session
                middle_sessions = []
                for cookie_jar in all_cookies:
                    if "middle_session" in cookie_jar:
                        middle_sessions.append(cookie_jar["middle_session"])

                middle_session = middle_sessions[0] if middle_sessions else None

                if access_token:
                    cookies_dict["access_token"] = access_token
                    cookies_dict["middle_session"] = middle_session
                    self._cookies = cookies_dict
                    return cookies_dict
                else:
                    raise Exception("登录失败，未获取到Cookie")
                    
            except Exception as e:
                logger.error(f"Wiki登录失败: {str(e)}")
                raise
                


        
    async def get_client(self) -> httpx.AsyncClient:
        """获取配置好Cookie的HTTP客户端"""
        if not self._client:
            cookie = await self.get_cookie()
            logger.info(f"创建HTTP客户端，Cookie: {cookie}",cookie)
            
            self._client = httpx.AsyncClient(
                follow_redirects=True,
                verify=False,
                timeout=30.0,
                headers={
                    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
            )
            if self._cookies:
                for name, value in self._cookies.items():
                    self._client.cookies.set(name, value)
            
        return self._client

        
    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.aclose()
            self._client = None

# 全局登录服务实例
_wiki_login_service: Optional[WikiLoginService] = None

async def get_wiki_login_service() -> WikiLoginService:
    """获取Wiki登录服务实例"""
    global _wiki_login_service
    if _wiki_login_service is None:
        _wiki_login_service = WikiLoginService()
    return _wiki_login_service