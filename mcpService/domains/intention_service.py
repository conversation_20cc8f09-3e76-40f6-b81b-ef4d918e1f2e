#!/usr/bin/env python3
"""
意愿域服务 - 签署意愿相关功能实现
提供意愿确认、意愿验证等功能
"""
import logging
from typing import Dict, Any, Optional
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class IntentionService(BaseService):
    """意愿服务类"""
    
    def __init__(self):
        super().__init__(domain="intention")


# 全局意愿服务实例
intention_service = IntentionService()


async def create_intention_verification(
    signer_name: str,
    signer_mobile: str,
    signer_idcard: str,
    document_title: str,
    verification_type: str = "SMS",
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建意愿验证
    
    Args:
        signer_name: 签署人姓名
        signer_mobile: 签署人手机号
        signer_idcard: 签署人身份证号
        document_title: 文档标题
        verification_type: 验证类型
        environment: 环境描述，支持自然语言
        
    Returns:
        创建结果
    """
    try:
        request_data = {
            "signerName": signer_name,
            "signerMobile": signer_mobile,
            "signerIdcard": signer_idcard,
            "documentTitle": document_title,
            "verificationType": verification_type
        }
        
        result = await intention_service.make_api_request(
            path="/intention/verification/create",
            data=request_data,
            environment=environment,
            operation="创建意愿验证"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"创建意愿验证异常: {str(e)}")
        return intention_service.formatter.error(
            message=f"创建意愿验证异常: {str(e)}",
            details={"signer_name": signer_name, "document_title": document_title}
        )


async def query_intention_status(
    intention_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询意愿状态
    
    Args:
        intention_id: 意愿ID
        environment: 环境描述，支持自然语言
        
    Returns:
        意愿状态
    """
    try:
        request_data = {
            "intentionId": intention_id
        }
        
        result = await intention_service.make_api_request(
            path="/intention/status",
            data=request_data,
            environment=environment,
            operation="查询意愿状态"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"查询意愿状态异常: {str(e)}")
        return intention_service.formatter.error(
            message=f"查询意愿状态异常: {str(e)}",
            details={"intention_id": intention_id}
        )


async def confirm_intention(
    intention_id: str,
    verification_code: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    确认意愿
    
    Args:
        intention_id: 意愿ID
        verification_code: 验证码
        environment: 环境描述，支持自然语言
        
    Returns:
        确认结果
    """
    try:
        request_data = {
            "intentionId": intention_id,
            "verificationCode": verification_code
        }
        
        result = await intention_service.make_api_request(
            path="/intention/confirm",
            data=request_data,
            environment=environment,
            operation="确认意愿"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"确认意愿异常: {str(e)}")
        return intention_service.formatter.error(
            message=f"确认意愿异常: {str(e)}",
            details={"intention_id": intention_id}
        )


async def cancel_intention(
    intention_id: str,
    reason: str = "测试取消",
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    取消意愿
    
    Args:
        intention_id: 意愿ID
        reason: 取消原因
        environment: 环境描述，支持自然语言
        
    Returns:
        取消结果
    """
    try:
        request_data = {
            "intentionId": intention_id,
            "reason": reason
        }
        
        result = await intention_service.make_api_request(
            path="/intention/cancel",
            data=request_data,
            environment=environment,
            operation="取消意愿"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"取消意愿异常: {str(e)}")
        return intention_service.formatter.error(
            message=f"取消意愿异常: {str(e)}",
            details={"intention_id": intention_id, "reason": reason}
        )


async def get_intention_record(
    signer_idcard: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取意愿记录
    
    Args:
        signer_idcard: 签署人身份证号
        start_date: 开始日期
        end_date: 结束日期
        environment: 环境描述，支持自然语言
        
    Returns:
        意愿记录
    """
    try:
        request_data = {
            "signerIdcard": signer_idcard
        }
        
        # 可选参数
        if start_date:
            request_data["startDate"] = start_date
        if end_date:
            request_data["endDate"] = end_date
        
        result = await intention_service.make_api_request(
            path="/intention/record",
            data=request_data,
            environment=environment,
            operation="获取意愿记录"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"获取意愿记录异常: {str(e)}")
        return intention_service.formatter.error(
            message=f"获取意愿记录异常: {str(e)}",
            details={"signer_idcard": signer_idcard}
        )