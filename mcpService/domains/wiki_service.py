"""
Wiki MCP Server 服务
提供Wiki内容访问、搜索、图片下载等功能
"""
import os
import uuid
import logging
from typing import Optional, Dict, Any
from pathlib import Path
from urllib.parse import quote, urlparse

import httpx
from bs4 import BeautifulSoup
from markdownify import markdownify as md

from app.core.config import get_wiki_config
from .wiki_login_service import get_wiki_login_service

logger = logging.getLogger(__name__)

class WikiService:
    """Wiki服务"""
    
    def __init__(self):
        self.config = get_wiki_config()
        
    async def download_image(self, image_url: str) -> str:
        """
        下载Wiki图片到本地
        
        Args:
            image_url: Wiki图片链接
            
        Returns:
            下载后的本地图片绝对路径
        """
        logger.info(f"开始下载图片: {image_url}")
        
        # 处理Wiki预览链接，转换为直接下载链接
        processed_url = self._process_image_url(image_url)
        logger.info(f"处理后的图片链接: {processed_url}")
        
        # 获取HTTP客户端
        login_service = await get_wiki_login_service()
        client = await login_service.get_client()
        
        try:
            # 下载图片
            response = await client.get(processed_url)
            response.raise_for_status()
            
            # 生成文件名
            file_extension = self._get_file_extension(processed_url)
            filename = f"{uuid.uuid4().hex}{file_extension}"
            
            # 确保downloads目录存在
            downloads_dir = Path("downloads")
            downloads_dir.mkdir(exist_ok=True)
            
            # 保存图片
            filepath = downloads_dir / filename
            with open(filepath, "wb") as f:
                f.write(response.content)
                
            logger.info(f"图片下载成功: {filepath}")
            return str(filepath.absolute())
            
        except Exception as e:
            logger.error(f"图片下载失败: {str(e)}")
            raise
            
    def _process_image_url(self, url: str) -> str:
        """处理Wiki图片链接，将预览链接转换为直接下载链接"""
        parsed = urlparse(url)
        
        # 如果是包含preview参数的预览链接，转换为直接下载链接
        if 'preview' in parsed.query:
            from urllib.parse import parse_qs
            query_params = parse_qs(parsed.query)
            if 'preview' in query_params:
                preview_path = query_params['preview'][0]
                # 提取pageId
                page_id = query_params.get('pageId', [''])[0]
                if page_id:
                    # 构建直接下载链接
                    base_url = f"{parsed.scheme}://{parsed.netloc}"
                    direct_url = f"{base_url}/download/attachments/{page_id}{preview_path}"
                    return direct_url
        
        return url
    
    def _get_file_extension(self, url: str) -> str:
        """从URL中获取文件扩展名"""
        parsed = urlparse(url)
        path = parsed.path
        
        # 从主路径中提取扩展名
        if '.' in path:
            return path[path.rfind('.'):]
        return '.png'  # 默认PNG格式
        
    async def access_link(self, wiki_link: str) -> str:
        """
        访问Wiki链接并转换为Markdown格式
        
        Args:
            wiki_link: Wiki链接（必须是 http://wiki.timevale.cn:8081）
            
        Returns:
            转换后的Markdown内容
        """
        logger.info(f"访问Wiki链接: {wiki_link}")
        
        # 验证链接域名
        if "wiki.timevale.cn:8081" not in wiki_link:
            raise ValueError("Wiki链接域名必须是 http://wiki.timevale.cn:8081")
            
        # 获取HTTP客户端
        login_service = await get_wiki_login_service()
        client = await login_service.get_client()
        
        try:
            # 访问Wiki页面
            response = await client.get(wiki_link)
            response.raise_for_status()
            
            # 转换为Markdown
            html_content = response.text
            markdown_content = self._html_to_markdown(html_content)
            
            logger.info(f"Wiki内容转换成功，长度: {len(markdown_content)}")
            return markdown_content
            
        except Exception as e:
            logger.error(f"访问Wiki链接失败: {str(e)}")
            raise
            
    async def wiki_query(self, keyword: str) -> str:
        """
        搜索Wiki内容
        
        Args:
            keyword: 搜索关键字
            
        Returns:
            搜索结果的Markdown格式内容
        """
        logger.info(f"搜索Wiki关键字: {keyword}")
        
        # URL编码关键字
        encoded_keyword = quote(keyword, encoding='utf-8')
        
        # 构建搜索URL
        search_url = f"{self.config['base_url']}/dosearchsite.action?queryString={encoded_keyword}"
        
        # 获取HTTP客户端
        login_service = await get_wiki_login_service()
        client = await login_service.get_client()
        
        try:
            # 执行搜索
            response = await client.get(search_url)
            logger.info(f"搜索请求状态码: {response.status_code}")
            logger.info(f"搜索请求URL: {search_url}")
            logger.info(f"响应头: {dict(response.headers)}")
            
            # 检查是否被重定向到登录页面
            if "login.action" in response.text or "zerotrust.esign.cn" in response.text or "用户登录" in response.text or "域账号" in response.text:
                logger.warning("检测到登录重定向，返回说明信息")
                # 返回一个说明信息
                return f"# Wiki搜索结果\n\n搜索关键字: {keyword}\n\n**注意**: 当前无法访问Wiki内容，可能需要配置正确的认证信息。\n\n请检查以下配置：\n1. 确保WIKI_COOKIE配置正确\n2. 或者确保WIKI_USERNAME和WIKI_PASSWORD配置正确\n\n当前配置：\n- Wiki服务器: {self.config['base_url']}\n- 登录URL: {self.config['login_url']}\n- 用户名: {self.config['username']}\n- Cookie配置: {'已配置' if self.config['cookie'] else '未配置'}"
            
            # 转换为Markdown
            html_content = response.text
            markdown_content = self._html_to_markdown(html_content)
            
            logger.info(f"Wiki搜索完成，结果长度: {len(markdown_content)}")
            return markdown_content
            
        except Exception as e:
            raise e
            
    def _html_to_markdown(self, html_content: str) -> str:
        """将HTML内容转换为Markdown格式"""
        try:
            # 使用BeautifulSoup清理HTML
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 移除不需要的元素
            for element in soup(['script', 'style', 'iframe', 'noscript']):
                element.decompose()
                
            # 使用markdownify转换为Markdown
            # 配置转换选项
            markdown_content = md(
                str(soup),
                heading_style="ATX",  # 使用 # 标题格式
                bullets="-",         # 使用 - 作为列表符号
                strip=['script', 'style', 'iframe', 'noscript']  # 移除不需要的标签
            )
            
            return markdown_content.strip()
            
        except Exception as e:
            logger.error(f"HTML转Markdown失败: {str(e)}")
            return f"转换失败: {str(e)}"

        
    async def close(self):
        """关闭服务连接"""
        login_service = await get_wiki_login_service()
        await login_service.close()

# 全局Wiki服务实例
_wiki_service: Optional[WikiService] = None

async def get_wiki_service() -> WikiService:
    """获取Wiki服务实例"""
    global _wiki_service
    if _wiki_service is None:
        _wiki_service = WikiService()
    return _wiki_service