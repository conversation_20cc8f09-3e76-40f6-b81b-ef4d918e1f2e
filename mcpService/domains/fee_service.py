#!/usr/bin/env python3
"""
费用域服务 - 企业充值相关功能实现
基于新的架构重构，使用统一的基础服务类
"""
import logging
from typing import Dict, Any

from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class FeeService(BaseService):
    """费用服务类"""

    def __init__(self):
        super().__init__(domain="fee")


# 全局费用服务实例
fee_service = FeeService()


async def add_app(
        env: str,
        name=None,
        gid=None
) -> Dict[str, Any]:
    """
    充值应用数量
    
    Args:
        env: 环境类型，如"测试环境，模拟环境"
        name: 企业名称，默认值为空
        gid: gid，默认值为空
    Returns:
        充值结果
    """
    request_data = {"env": env}
    # 只要传了gid就优先按gid充值，否则按name充值
    if gid:
        request_data["gid"] = gid
    else:
        request_data["name"] = name
    headers = {'operator': 'mcp'}
    result = await fee_service.make_api_request(
        url="http://sdk.smlk8s.esign.cn/fee/app",
        data=request_data,
        method="POST",
        service="fee",
        operation="充值应用数量",
        headers=headers
    )
    return result


async def recharge_quota(
        env: str,
        name=None,
        gid=None,
        appId=None
) -> Dict[str, Any]:
    """
    充值账户余额

    Args:
        env: 环境类型，如"测试环境，模拟环境"
        name: 企业名称，默认值为空
        gid: gid，默认值为空
        appId: appId，默认位为空
    Returns:
        充值结果
    """
    request_data = {"env": env}
    # 只要传了gid就优先按gid充值，否则按name充值
    if gid:
        request_data["gid"] = gid
    elif appId:
        request_data["appId"] = appId
    else:
        request_data["name"] = name
    headers = {'operator': 'mcp'}
    result = await fee_service.make_api_request(
        url="http://sdk.smlk8s.esign.cn/fee/recharge",
        data=request_data,
        method="POST",
        service="fee",
        operation="充值余额",
        headers=headers
    )
    return result
