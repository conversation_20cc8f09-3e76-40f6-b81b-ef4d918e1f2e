import copy
import json
import random

import requests

from main import logger
from mcpService.common.OpenApiFactory import OpenApiFactory


def get_signers(环境="测试环境", 签署方列表=[], 有序签=False):
    data_provider = OpenApiFactory.create(环境)
    signer = {
        "orgSignerInfo": {
            "orgName": data_provider.default_org_name,
            "transactorInfo": {
                "psnAccount": data_provider.default_phone
            }
        },
        "psnSignerInfo": {
            "psnAccount": data_provider.default_phone
        },
        "signConfig": {
            "forcedReadingTime": 0,
            "signOrder": 1,
            "signTaskType": 0
        },
        "signFields": [
            {
                "customBizNum": "",
                "fileId": data_provider.default_file_id,
                "normalSignFieldConfig": {
                    "autoSign": False,
                    "freeMode": False,
                    "movableSignField": True,
                    "psnSealStyles": "",
                    "signFieldPosition": {
                        "acrossPageMode": "ALL",
                        "acrossPageOffset": 0,
                        "positionPage": "1",
                        "positionX": 1,
                        "positionY": 1
                    },
                    "signFieldStyle": 1
                },
                "remarkSignFieldConfig": {
                    "aiCheck": 0,
                    "freeMode": False,
                    "inputType": 1,
                    "movableSignField": True,
                    "remarkContent": "1234",
                    "remarkFontSize1": 20,
                    "signFieldHeight": 100,
                    "signFieldPosition": {
                        "acrossPageMode": "",
                        "acrossPageOffset": 0,
                        "positionPage": "1",
                        "positionX": 1,
                        "positionY": 1
                    },
                    "signFieldWidth": 400
                },
                "signFieldType": 0,
                "signFieldType说明": "签署区类型 0-普通签章区 1-备注签字区"
            }
        ],
        "signerType": 0,
        "signerType说明": "0 - 个人，1 - 企业/机构，2 - 法定代表人，3 - 经办人"
    }
    signers = []
    for i, 签署方 in enumerate(签署方列表, start=1):
        sign_temp = copy.deepcopy(signer)
        if '签署人类型' not in 签署方:
            签署方['签署人类型'] = '个人'
        if '签署区类型' not in 签署方:
            签署方['签署区类型'] = '普通'
        if '签署区样式' not in 签署方:
            签署方['签署区样式'] = '单页'
        if '个人' in 签署方['签署人类型']:
            del sign_temp["orgSignerInfo"]
            sign_temp['signerType'] = 0
        else:
            del sign_temp["psnSignerInfo"]
            sign_temp['signerType'] = 1
        if '备注' in 签署方['签署区类型']:
            del sign_temp["signFields"][0]["normalSignFieldConfig"]
            sign_temp['signFields'][0]['remarkSignFieldConfig']['signFieldPosition']['positionX'] = random.uniform(100,
                                                                                                                   500)
            sign_temp['signFields'][0]['remarkSignFieldConfig']['signFieldPosition']['positionY'] = random.uniform(400,
                                                                                                                   700)
            sign_temp['signFields'][0]['signFieldType'] = 1
        else:
            del sign_temp["signFields"][0]["remarkSignFieldConfig"]
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldPosition']['positionX'] = random.uniform(100,
                                                                                                                   500)
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldPosition']['positionY'] = random.uniform(400,
                                                                                                                   700)
        if '自由' in 签署方['签署区样式'] and ('备注' not in 签署方['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['freeMode'] = True
        if '自由' in 签署方['签署区样式'] and ('备注' in 签署方['签署区类型']):
            sign_temp['signFields'][0]['remarkSignFieldConfig']['freeMode'] = True
        if '单页' in 签署方['签署区样式'] and ('备注' not in 签署方['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldStyle'] = 1
        elif '骑缝' in 签署方['签署区样式'] and ('备注' not in 签署方['签署区类型']):
            sign_temp['signFields'][0]['normalSignFieldConfig']['signFieldStyle'] = 2
        else:
            pass
        if 有序签:
            sign_temp['signConfig']['signOrder'] = i
        signers.append(sign_temp)

    return {"docs": [{"fileId": data_provider.default_file_id}],
            "signFlowConfig": {"autoFinish": True, "autoStart": True, "identityVerify": False,
                               "signFlowTitle": "MCP发起的流程"},
            "signers": signers}


def getSignUrl(环境="测试环境", 签署方列表=None, group="default",
               有序签=False):
    if 签署方列表 is None:
        签署方列表 = [{"签署人类型": "个人", "签署区类型": "普通"}]
    data_provider = OpenApiFactory.create(环境)
    headers = {
        'X-Tsign-Open-App-Id': data_provider.default_app_id,
        'X-Tsign-Open-Auth-Mode': 'simple',
        'Content-Type': 'application/json;charset=UTF-8',
        'X-Tsign-Service-Group': group
    }

    request_data = get_signers(环境=环境, 签署方列表=签署方列表, 有序签=有序签)
    url = data_provider.open_api_url + '/v3/sign-flow/create-by-file'
    response = requests.post(url, headers=headers, json=request_data)

    # 打印响应
    logger.info(f"发起签署的请求体：{json.dumps(request_data, ensure_ascii=False)}")
    res = response.json()
    logger.info(f"响应体：{json.dumps(res, ensure_ascii=False)}")
    flowId = res.get("data").get("signFlowId")
    logger.info("获取的flowid是{}".format(flowId))

    # 获取签署链接
    url = data_provider.open_api_url + '/v3/sign-flow/{}/sign-url'.format(flowId)
    data = {
        "operator": {
            "psnAccount": data_provider.default_phone
        },
        "clientType": "ALL",
        "appScheme": "",
        "needLogin": False
    }
    response = requests.post(url, headers=headers, data=json.dumps(data, ensure_ascii=False))
    res = response.json()
    logger.info(f"获取签署链接：{res.get('data').get('shortUrl')}")


if __name__ == '__main__':
    getSignUrl(环境="测试环境",
               签署方列表=[{"签署人类型": "个人", "签署区类型": "备注", "签署区样式": "自由"},
                           {"签署人类型": "企业", "签署区类型": "普通", "签署区样式": "骑缝"}],
               有序签=False,
               group="default")
