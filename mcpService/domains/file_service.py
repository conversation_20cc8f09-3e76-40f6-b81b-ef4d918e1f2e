#!/usr/bin/env python3
"""
文件服务
"""
import base64
import hashlib
import logging
import os
from typing import Dict, Any

import requests

from mcpService.common.OpenApiFactory import OpenApiFactory
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class FileService(BaseService):
    """签署服务类"""

    def __init__(self):
        super().__init__(domain="file")


# 全局签署服务实例
file_service = FileService()


async def get_upload_url(
        env: str,
        path: str = './data/3页.pdf',
        convert2Pdf: bool = False,
        dedicatedCloud: bool = False,
        appId: str = None
) -> Dict[str, Any]:
    """
    一键签署

    Args:
        env: 环境类型，如"测试环境"，"模拟环境"
        path: 文件路径
        convert2Pdf: 是否转化为pfd，默认为否
        dedicatedCloud: 专属云项目id，默认为空
        appId: appId
    Returns:
        文件id和上传oss链接
    """
    global request_data
    if appId is None or appId == "":
        appId = OpenApiFactory.create(env).default_app_id
    md5 = get_file_md5(path)
    file_name = os.path.basename(path)
    try:
        # 构建请求数据
        request_data = {
            "contentMd5": md5,
            "contentType": "application/octet-stream;charset=UTF-8",
            "convert2Pdf": convert2Pdf,
            "fileName": file_name,
            "fileSize": 2542635
        }
        if dedicatedCloud:
            request_data["dedicatedCloudId"] = OpenApiFactory.create(env).dedicated_id
            appId = OpenApiFactory.create(env).beike_app_id

        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-App-Id": appId
        }

        result = await file_service.make_api_request(
            url=OpenApiFactory.create(env).open_api_url + "/v1/files/getUploadUrl",
            data=request_data,
            method="POST",
            service="sign",
            operation="获取文件id",
            headers=headers
        )

        if result['status'] is not "success":
            return result['details']

        file_id = result['data']['data']['fileId']
        url = result['data']['data']['uploadUrl']
        upload_headers = {
            "Content-MD5": md5,
            "Content-Type": "application/octet-stream;charset=UTF-8"
        }
        with open(path, 'rb') as file_data:
            upload_response = requests.put(url, data=file_data, headers=upload_headers)

        response_data = {
            "success": True,
            "message": "成功",
            "data": {
                "fileId": file_id,
                "fileMd5": md5,
                "appId": appId,
                "fileName": file_name,
                "uploadStatus": "success"
            }
        }
        if dedicatedCloud:
            response_data['data']['dedicatedCloudId'] = OpenApiFactory.create(env).dedicated_id
        if upload_response.status_code != 200:
            response_data['success'] = False
            response_data['message'] = f"文件上传失败: {upload_response.status_code} - {upload_response.text}"
        return response_data

    except Exception as e:
        logger.error(f"获取文件id异常: {str(e)}")
        return file_service.formatter.error(
            message=f"获取文件id异常: {str(e)}",
            details=request_data
        )


def get_file_md5(file_path: str) -> str:
    """获取文件的Base64编码MD5值"""
    try:
        with open(file_path, 'rb') as fd:
            m = hashlib.md5()
            while True:
                d = fd.read(8096)
                if not d:
                    break
                m.update(d)
            byte = base64.b64encode(m.digest())
            return bytes.decode(byte)
    except Exception as e:
        logger.error(f"计算文件MD5失败: {str(e)}")
        raise
