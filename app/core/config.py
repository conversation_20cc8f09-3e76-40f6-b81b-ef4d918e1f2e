"""
简化配置 - 直接使用环境变量，无冗余层次
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
app_env_file = Path(__file__).parent.parent / ".env"
if app_env_file.exists():
    load_dotenv(app_env_file)
    print(f"✅ 已加载配置文件: {app_env_file}")

# 项目基本信息
PROJECT_NAME = "esign-qa-mcp-platform"
VERSION = "2.0.0"
MCP_NAME = "E-Sign QA Data Platform"
MCP_DESCRIPTION = "电子签名QA造数平台"

# 基础配置
BASE_URL = os.getenv("base_url", "http://in-test-openapi.tsign.cn")
APP_ID = os.getenv("app_id", "3438757422")
IS_MOCK = os.getenv("is_mock", "false").lower() == "true"

# 请求头配置
X_TSIGN_OPEN_TENANT_ID = os.getenv("X-Tsign-Open-Tenant-Id", "b0f99abbc3cd4d63a5a2a84c452e52d6")

# 请求配置
REQUEST_TIMEOUT = 30

# 环境管理
current_environment = "test"

def detect_environment_from_text(text: str) -> str:
    """从文本中检测环境类型"""
    if not text:
        return current_environment
    
    if "mock" in text.lower() or "模拟" in text.lower():
        return "mock"
    return "test"

def get_api_config(env: str = None):
    """获取API配置"""
    current_env = env or current_environment

    if current_env == "mock" or IS_MOCK:
        return {
            "base_url": os.getenv("mock_base_url", "http://mock-openapi.tsign.cn"),
            "app_id": os.getenv("mock_app_id", "mock_app_id"),
            "environment": "mock"
        }
    else:
        return {
            "base_url": BASE_URL,
            "app_id": APP_ID,
            "headers": {
                "Content-Type": "application/json",
                "X-Tsign-Open-App-Id": APP_ID,
                "X-Tsign-Open-Tenant-Id": X_TSIGN_OPEN_TENANT_ID,
                "X-Tsign-Open-Auth-Mode": "simple",
                "filter-result": "false"
            },
            "environment": current_env,
            "timeout": REQUEST_TIMEOUT
        }

def get_service_config(service: str = None, env: str = None):
    """获取服务配置"""
    api_config = get_api_config(env)
    
    return {
        "url": api_config.get("base_url"),
        "app_id": api_config.get("app_id"),
        "headers": api_config.get("headers", {}),
        "environment": api_config.get("environment", "test"),
        "timeout": REQUEST_TIMEOUT
    }

# Wiki MCP Server 配置
WIKI_BASE_URL = os.getenv("WIKI_BASE_URL", "http://wiki.timevale.cn:8081")
WIKI_LOGIN_URL = os.getenv("WIKI_LOGIN_URL", "https://zerotrust.esign.cn/submit")
WIKI_USERNAME = os.getenv("WIKI_USERNAME", "afei")
WIKI_PASSWORD = os.getenv("WIKI_PASSWORD", "feiA12345@")
WIKI_COOKIE = os.getenv("WIKI_COOKIE", "")

def get_wiki_config():
    """获取Wiki服务配置"""
    return {
        "base_url": WIKI_BASE_URL,
        "login_url": WIKI_LOGIN_URL,
        "username": WIKI_USERNAME,
        "password": WIKI_PASSWORD,
        "cookie": WIKI_COOKIE
    }
