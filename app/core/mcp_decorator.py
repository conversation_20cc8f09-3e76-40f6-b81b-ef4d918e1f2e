#!/usr/bin/env python3
"""
MCP装饰器模块
集成参数校验、调用统计、异常处理的统一装饰器
"""
import inspect
import logging
import re
from functools import wraps
from typing import get_type_hints, Optional, Any, Dict, List
from fastapi import Query, Path, Body
from app.core.call_stats import call_stats_manager

logger = logging.getLogger(__name__)


class ValidationResult:
    """参数校验结果"""
    def __init__(self, is_valid: bool, errors: List[str], suggestions: List[str]):
        self.is_valid = is_valid
        self.errors = errors
        self.suggestions = suggestions


def mcp_endpoint(func):
    """
    MCP端点装饰器 - 集成参数校验、调用统计、异常处理
    
    功能：
    1. 智能参数校验和错误提示
    2. API调用次数统计
    3. 统一的异常处理
    4. 自动解析函数签名
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        method_name = func.__name__

        try:
            # 1. API调用统计
            call_stats_manager.increment_call(method_name)

            # 2. 参数校验
            sig = inspect.signature(func)
            param_schema = extract_param_schema(sig)
            validation_result = validate_params_smart(kwargs, param_schema)

            if not validation_result.is_valid:
                return {
                    "status": "parameter_error",
                    "message": "参数校验失败，请检查以下问题：",
                    "errors": validation_result.errors,
                    "required_params": param_schema["required"],
                    "optional_params": param_schema["optional"],
                    "suggestions": validation_result.suggestions,
                    "function_name": method_name,
                    "call_count": call_stats_manager.get_call_count(method_name),
                    "help": f"接口 {method_name} 的参数说明"
                }

            # 3. 执行业务逻辑
            result = await func(*args, **kwargs)

            # 4. 添加元数据（可选，只对dict类型的返回值）
            if isinstance(result, dict) and "status" not in result:
                result["_meta"] = {
                    "call_count": call_stats_manager.get_call_count(method_name),
                    "function_name": method_name,
                    "success": True
                }

            return result

        except Exception as e:
            logger.error(f"❌ {method_name} 执行异常: {e}")
            return {
                "status": "error",
                "message": f"接口执行异常: {str(e)}",
                "function_name": method_name,
                "call_count": call_stats_manager.get_call_count(method_name),
                "error_type": type(e).__name__
            }

    return wrapper


def extract_param_schema(signature) -> Dict[str, Dict[str, Any]]:
    """从函数签名中提取参数schema"""
    required = {}
    optional = {}

    for param_name, param in signature.parameters.items():
        if param_name in ['args', 'kwargs', 'self']:  # 跳过特殊参数
            continue

        param_info = {
            "type": get_param_type(param),
            "description": "",
            "examples": []
        }

        # 解析Query/Path/Body等FastAPI注解
        if param.default != inspect.Parameter.empty:
            default_value = param.default

            # 处理FastAPI的Query/Path/Body等
            if hasattr(default_value, 'default'):  # Query(...) 或 Query("default")
                # 提取description
                if hasattr(default_value, 'description') and default_value.description:
                    param_info["description"] = default_value.description
                    # 从description中提取示例
                    param_info["examples"] = extract_examples_from_description(default_value.description)

                # 检查是否为必填参数
                try:
                    from pydantic_core import PydanticUndefined
                except ImportError:
                    # 兼容不同版本的pydantic
                    PydanticUndefined = type(default_value.default)

                if (default_value.default == ... or
                    default_value.default is ... or
                    str(default_value.default) == "PydanticUndefined" or
                    type(default_value.default).__name__ == "PydanticUndefined"):  # Query(...)表示必填
                    required[param_name] = param_info
                else:  # Query("default")表示可选
                    param_info["default"] = default_value.default
                    optional[param_name] = param_info

            else:  # 普通默认值
                param_info["default"] = default_value
                optional[param_name] = param_info
        else:
            # 没有默认值，视为必填
            required[param_name] = param_info

    return {"required": required, "optional": optional}


def extract_examples_from_description(description: str) -> List[str]:
    """从description中提取示例"""
    examples = []

    # 匹配各种示例模式
    patterns = [
        r"如['\"]([^'\"]+)['\"]",
        r"例如['\"]([^'\"]+)['\"]",
        r"示例[:\s]*['\"]([^'\"]+)['\"]",
        r"默认['\"]([^'\"]+)['\"]",
        r"比如['\"]([^'\"]+)['\"]"
    ]

    for pattern in patterns:
        matches = re.findall(pattern, description)
        examples.extend(matches)

    # 去重并保持顺序
    seen = set()
    unique_examples = []
    for example in examples:
        if example not in seen:
            seen.add(example)
            unique_examples.append(example)

    return unique_examples


def get_param_type(param) -> str:
    """获取参数类型"""
    if param.annotation != inspect.Parameter.empty:
        annotation = param.annotation
        if hasattr(annotation, '__name__'):
            return annotation.__name__
        else:
            # 处理Optional[str]等复杂类型
            return str(annotation).replace('typing.', '')
    return "any"


def validate_params_smart(params: Dict[str, Any], schema: Dict[str, Dict[str, Any]]) -> ValidationResult:
    """智能参数校验"""
    errors = []
    suggestions = []

    # 检查必填参数
    for param_name, param_config in schema["required"].items():
        if param_name not in params or params[param_name] is None or params[param_name] == "":
            errors.append(f"缺少必填参数: {param_name}")

            # 生成建议
            suggestion = f"请提供 {param_name} 参数"
            if param_config["description"]:
                suggestion += f" ({param_config['description']})"
            if param_config["examples"]:
                suggestion += f"，例如: {param_config['examples'][0]}"
            suggestions.append(suggestion)

    # 检查参数格式和有效性
    for param_name, value in params.items():
        if param_name in schema["required"] or param_name in schema["optional"]:
            param_config = schema["required"].get(param_name) or schema["optional"].get(param_name)

            if value is not None and value != "":
                # 基于description和类型进行校验
                validation_errors, validation_suggestions = validate_param_value(
                    param_name, value, param_config
                )
                errors.extend(validation_errors)
                suggestions.extend(validation_suggestions)

    return ValidationResult(
        is_valid=len(errors) == 0,
        errors=errors,
        suggestions=suggestions
    )


def validate_param_value(param_name: str, value: Any, param_config: Dict[str, Any]) -> tuple:
    """校验单个参数值"""
    errors = []
    suggestions = []

    description = param_config.get("description", "").lower()
    param_type = param_config.get("type", "")
    examples = param_config.get("examples", [])

    # ID类型校验
    if "id" in param_name.lower() or "id" in description:
        if isinstance(value, str) and len(value) < 8:
            errors.append(f"{param_name} 格式可能不正确，ID通常较长")
            if examples:
                suggestions.append(f"{param_name} 示例: {examples[0]}")

    # 环境类型校验
    if "环境" in description or "env" in param_name.lower():
        valid_envs = ["测试环境", "模拟环境", "test", "mock", "prod", "生产环境", "sml", "moni"]
        if isinstance(value, str) and value not in valid_envs:
            errors.append(f"{param_name} 环境类型可能不正确: '{value}'")
            suggestions.append(f"{param_name} 支持的环境: {', '.join(valid_envs[:4])}")

    # 手机号校验
    if "手机" in description or "mobile" in param_name.lower() or "phone" in param_name.lower():
        if isinstance(value, str) and not re.match(r'^1[3-9]\d{9}$', value):
            errors.append(f"{param_name} 手机号格式不正确")
            suggestions.append(f"{param_name} 示例: 13800138000")

    # 身份证号校验
    if "身份证" in description or "idcard" in param_name.lower():
        if isinstance(value, str) and not re.match(r'^\d{17}[\dXx]$', value):
            errors.append(f"{param_name} 身份证号格式不正确")
            suggestions.append(f"{param_name} 示例: 110101199001011234")

    return errors, suggestions