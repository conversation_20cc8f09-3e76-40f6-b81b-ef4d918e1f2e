#!/usr/bin/env python3
"""
自动发现模块
自动发现控制器和标签，避免手动维护
"""
import os
import importlib
import inspect
from pathlib import Path
from typing import List, Dict, Set
import logging

logger = logging.getLogger(__name__)


def discover_controller_modules(base_path: str = "app/mcpController/domains") -> List[str]:
    """
    自动发现控制器模块
    
    Args:
        base_path: 控制器基础路径
        
    Returns:
        List[str]: 模块路径列表
    """
    modules = []
    
    try:
        # 获取控制器目录
        controllers_dir = Path(base_path)
        
        if not controllers_dir.exists():
            logger.warning(f"控制器目录不存在: {controllers_dir}")
            return modules
        
        # 遍历所有Python文件
        for py_file in controllers_dir.glob("*.py"):
            if py_file.name == "__init__.py":
                continue
                
            # 构建模块路径
            module_path = f"{base_path.replace('/', '.')}.{py_file.stem}"
            modules.append(module_path)
            
        logger.info(f"发现 {len(modules)} 个控制器模块")
        for module in modules:
            logger.debug(f"  - {module}")
            
        return modules
        
    except Exception as e:
        logger.error(f"发现控制器模块失败: {e}")
        return []


def discover_router_tags(modules: List[str]) -> List[str]:
    """
    自动发现路由标签
    
    Args:
        modules: 模块路径列表
        
    Returns:
        List[str]: 标签列表
    """
    tags = set()
    
    for module_path in modules:
        try:
            # 动态导入模块
            module = importlib.import_module(module_path)
            
            # 查找路由器对象
            router_candidates = [
                'router', 'app_router', f'{module_path.split(".")[-1]}_router'
            ]
            
            router = None
            for candidate in router_candidates:
                if hasattr(module, candidate):
                    router = getattr(module, candidate)
                    break
            
            if router and hasattr(router, 'tags'):
                # 提取标签
                if router.tags:
                    tags.update(router.tags)
                    logger.debug(f"从 {module_path} 提取标签: {router.tags}")
            
        except Exception as e:
            logger.warning(f"处理模块 {module_path} 时出错: {e}")
            continue
    
    tags_list = list(tags)
    logger.info(f"发现 {len(tags_list)} 个路由标签: {tags_list}")
    
    return tags_list


def get_controller_info(modules: List[str]) -> Dict[str, Dict]:
    """
    获取控制器详细信息
    
    Args:
        modules: 模块路径列表
        
    Returns:
        Dict: 控制器信息字典
    """
    controller_info = {}
    
    for module_path in modules:
        try:
            module = importlib.import_module(module_path)
            
            info = {
                "module_path": module_path,
                "module_name": module_path.split(".")[-1],
                "tags": [],
                "endpoints": [],
                "router_found": False
            }
            
            # 查找路由器
            router_candidates = [
                'router', 'app_router', f'{module_path.split(".")[-1]}_router'
            ]
            
            for candidate in router_candidates:
                if hasattr(module, candidate):
                    router = getattr(module, candidate)
                    info["router_found"] = True
                    
                    if hasattr(router, 'tags') and router.tags:
                        info["tags"] = router.tags
                    
                    # 获取端点信息
                    if hasattr(router, 'routes'):
                        for route in router.routes:
                            if hasattr(route, 'endpoint'):
                                endpoint_name = route.endpoint.__name__ if route.endpoint else "unknown"
                                info["endpoints"].append({
                                    "name": endpoint_name,
                                    "path": getattr(route, 'path', 'unknown'),
                                    "methods": getattr(route, 'methods', set())
                                })
                    break
            
            controller_info[module_path] = info
            
        except Exception as e:
            logger.warning(f"获取 {module_path} 信息时出错: {e}")
            controller_info[module_path] = {
                "module_path": module_path,
                "error": str(e),
                "router_found": False
            }
    
    return controller_info


def auto_discover_all() -> Dict[str, any]:
    """
    自动发现所有信息
    
    Returns:
        Dict: 包含模块、标签等信息的字典
    """
    logger.info("开始自动发现...")
    
    # 发现模块
    modules = discover_controller_modules()
    
    # 发现标签
    tags = discover_router_tags(modules)
    
    # 获取详细信息
    controller_info = get_controller_info(modules)
    
    result = {
        "modules": modules,
        "tags": tags,
        "controller_info": controller_info,
        "summary": {
            "total_modules": len(modules),
            "total_tags": len(tags),
            "successful_modules": len([info for info in controller_info.values() if info.get("router_found", False)])
        }
    }
    
    logger.info(f"自动发现完成: {result['summary']}")
    
    return result


def validate_discovery_result(result: Dict[str, any]) -> bool:
    """
    验证发现结果
    
    Args:
        result: 发现结果
        
    Returns:
        bool: 是否有效
    """
    if not result.get("modules"):
        logger.error("未发现任何控制器模块")
        return False
    
    if not result.get("tags"):
        logger.warning("未发现任何路由标签")
    
    successful_count = result.get("summary", {}).get("successful_modules", 0)
    total_count = result.get("summary", {}).get("total_modules", 0)
    
    if successful_count == 0:
        logger.error("没有成功加载的控制器模块")
        return False
    
    if successful_count < total_count:
        logger.warning(f"部分模块加载失败: {successful_count}/{total_count}")
    
    return True


# 便捷函数
def get_modules_and_tags() -> tuple:
    """
    获取模块列表和标签列表
    
    Returns:
        tuple: (modules, tags)
    """
    result = auto_discover_all()
    
    if not validate_discovery_result(result):
        logger.error("自动发现失败，使用默认配置")
        # 返回默认配置
        default_modules = [
            "app.mcpController.domains.certificate_controller",
            "app.mcpController.domains.signing_controller",
            "app.mcpController.domains.saas_controller",
            "app.mcpController.domains.identity_controller",
            "app.mcpController.domains.intention_controller",
            "app.mcpController.domains.platform_controller",
            "app.mcpController.domains.fee_controller",
            "app.mcpController.domains.wiki_controller"
        ]
        default_tags = ["证书域", "签署域", "SaaS域", "实名域", "意愿域", "平台功能", "费用域", "wiki域"]
        return default_modules, default_tags
    
    return result["modules"], result["tags"]


if __name__ == "__main__":
    # 测试自动发现功能
    logging.basicConfig(level=logging.INFO)
    
    print("=" * 60)
    print("测试自动发现功能")
    print("=" * 60)
    
    result = auto_discover_all()
    
    print(f"\n发现的模块 ({len(result['modules'])}):")
    for module in result["modules"]:
        print(f"  - {module}")
    
    print(f"\n发现的标签 ({len(result['tags'])}):")
    for tag in result["tags"]:
        print(f"  - {tag}")
    
    print(f"\n控制器详情:")
    for module_path, info in result["controller_info"].items():
        print(f"  {module_path}:")
        print(f"    路由器: {'✅' if info.get('router_found') else '❌'}")
        print(f"    标签: {info.get('tags', [])}")
        print(f"    端点数: {len(info.get('endpoints', []))}")
    
    print(f"\n汇总: {result['summary']}")