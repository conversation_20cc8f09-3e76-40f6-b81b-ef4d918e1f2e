#!/usr/bin/env python3
"""
证书域控制器 - 数字证书相关功能
提供证书创建、查询、吊销等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

# 异步导入所有服务函数
from mcpService.domains.certificate_service import (
    create_certificate, query_certificate_detail, 
    revoke_certificate, update_certificate, get_user_info
)
from mcpService.domains.certificate_service import get_test_account
from app.core.mcp_decorator import mcp_endpoint

# 配置日志
logger = logging.getLogger(__name__)

# 创建证书域路由器
certificate_router = APIRouter(
    prefix="/certificate",
    tags=["证书域"],
    responses={404: {"description": "Not found"}}
)


@certificate_router.get(
    "/get_test_account",
    summary="📱 获取测试账号",
    description="获取测试账号信息，包括手机号、证件号、姓名等，支持环境切换",
    operation_id="certificate_get_test_account"
)
@mcp_endpoint
async def get_test_account_endpoint(
    environment: Optional[str] = Query(None, description="环境类型，支持自然语言描述")
):
    """获取测试账号"""
    return await get_test_account(environment)


@certificate_router.post(
    "/create",
    summary="🔐 创建证书",
    description="创建数字证书，支持多种算法和有效期",
    operation_id="certificate_create"
)
@mcp_endpoint
async def create_certificate_endpoint(
    cert_name: str,
    phone: str,
    idcard: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建证书"""
    return await create_certificate(
        cert_name, phone, idcard, algorithm, 
        cert_time, app_id, environment
    )


@certificate_router.post(
    "/query_detail",
    summary="🔍 查询证书详情",
    description="查询证书详情信息",
    operation_id="certificate_query_detail"
)
@mcp_endpoint
async def query_certificate_detail_endpoint(
    cert_id: str,
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询证书详情"""
    return await query_certificate_detail(cert_id, app_id, environment)


@certificate_router.post(
    "/revoke",
    summary="❌ 吊销证书",
    description="吊销数字证书",
    operation_id="certificate_revoke"
)
@mcp_endpoint
async def revoke_certificate_endpoint(
    cert_info_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """吊销证书"""
    return await revoke_certificate(cert_info_id, environment)


@certificate_router.post(
    "/update",
    summary="✏️ 更新证书",
    description="更新数字证书信息",
    operation_id="certificate_update"
)
@mcp_endpoint
async def update_certificate_endpoint(
    cert_info_id: str,
    cert_name: str,
    mobile: str,
    license_number: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    config_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """更新证书"""
    return await update_certificate(
        cert_info_id, cert_name, mobile, license_number,
        algorithm, cert_time, config_id, environment
    )


@certificate_router.get(
    "/get_user_info",
    summary="👤 获取用户信息",
    description="获取用户信息",
    operation_id="certificate_get_user_info"
)
@mcp_endpoint
async def get_user_info_endpoint(
    user_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """获取用户信息"""
    return await get_user_info(user_id, environment)


# 导出router供main.py使用
router = certificate_router