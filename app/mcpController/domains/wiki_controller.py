"""
Wiki MCP Server 控制器
提供Wiki相关的API接口
"""
import logging
import time
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from mcpService.domains.wiki_service import get_wiki_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/wiki", tags=["wiki域"])

# 请求模型
class ImageDownloadRequest(BaseModel):
    image_url: str = Field(..., description="Wiki图片链接")

class WikiLinkRequest(BaseModel):
    wiki_link: str = Field(..., description="Wiki链接")

class WikiSearchRequest(BaseModel):
    keyword: str = Field(..., description="搜索关键字")

# 响应模型
class WikiResponse(BaseModel):
    success: bool = True
    data: Any
    message: str = "操作成功"
    timestamp: int

@router.post("/download-image", response_model=WikiResponse)
async def download_image(request: ImageDownloadRequest):
    """
    下载Wiki图片到本地
    
    Args:
        request: 包含图片链接的请求
        
    Returns:
        下载后的本地图片绝对路径
    """
    try:
        wiki_service = await get_wiki_service()
        file_path = await wiki_service.download_image(request.image_url)
        
        return WikiResponse(
            data={
                "file_path": file_path,
                "image_url": request.image_url
            },
            message="图片下载成功",
            timestamp=int(time.time())
        )
        
    except Exception as e:
        logger.error(f"图片下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/access-link", response_model=WikiResponse)
async def access_link(request: WikiLinkRequest):
    """
    访问Wiki链接并转换为Markdown格式
    
    Args:
        request: 包含Wiki链接的请求
        
    Returns:
        转换后的Markdown内容
    """
    try:
        wiki_service = await get_wiki_service()
        markdown_content = await wiki_service.access_link(request.wiki_link)
        
        return WikiResponse(
            data={
                "markdown_content": markdown_content,
                "wiki_link": request.wiki_link,
                "content_length": len(markdown_content)
            },
            message="Wiki内容获取成功",
            timestamp=int(time.time())
        )
        
    except Exception as e:
        logger.error(f"访问Wiki链接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=WikiResponse)
async def wiki_search(request: WikiSearchRequest):
    """
    搜索Wiki内容
    
    Args:
        request: 包含搜索关键字的请求
        
    Returns:
        搜索结果的Markdown格式内容
    """
    try:
        wiki_service = await get_wiki_service()
        search_results = await wiki_service.wiki_query(request.keyword)
        
        return WikiResponse(
            data={
                "search_results": search_results,
                "keyword": request.keyword,
                "results_length": len(search_results)
            },
            message="Wiki搜索完成",
            timestamp=int(time.time())
        )
        
    except Exception as e:
        logger.error(f"Wiki搜索失败: {str(e)}",e)
        raise HTTPException(status_code=500, detail=str(e))
