"""
业务域控制器模块
按业务域组织MCP接口，提供统一的造数平台服务
自动发现并导入所有控制器路由
"""
import os
import importlib
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def auto_import_routers():
    """自动发现并导入所有控制器路由"""
    routers = {}
    all_exports = []
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    
    # 遍历所有Python文件
    for py_file in current_dir.glob("*.py"):
        if py_file.name in ["__init__.py"]:
            continue
            
        module_name = py_file.stem
        
        try:
            # 动态导入模块
            module = importlib.import_module(f".{module_name}", package=__name__)
            
            # 查找路由器对象
            router_candidates = [
                'router',
                f'{module_name}_router',
                f'{module_name.replace("_controller", "")}_router'
            ]
            
            router_found = False
            for candidate in router_candidates:
                if hasattr(module, candidate):
                    router = getattr(module, candidate)
                    router_name = f"{module_name.replace('_controller', '')}_router"
                    routers[router_name] = router
                    all_exports.append(router_name)
                    
                    # 添加到全局命名空间
                    globals()[router_name] = router
                    
                    logger.debug(f"✅ 自动导入路由: {router_name} from {module_name}")
                    router_found = True
                    break
            
            if not router_found:
                logger.warning(f"⚠️ 未找到路由器: {module_name}")
                
        except Exception as e:
            logger.error(f"❌ 导入模块失败 {module_name}: {e}")
            continue
    
    logger.info(f"🔍 自动发现并导入了 {len(routers)} 个路由器")
    return routers, all_exports

# 执行自动导入
_routers, __all__ = auto_import_routers()

# 为了向后兼容，也可以通过属性访问
def __getattr__(name):
    """动态属性访问，支持向后兼容"""
    if name in _routers:
        return _routers[name]
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
