#!/usr/bin/env python3
"""
签署域控制器 - 电子签名相关功能
提供合同签署、签署流程管理等造数功能
"""
import logging
from typing import Optional

from fastapi import APIRouter, Query

from app.core.mcp_decorator import mcp_endpoint
from mcpService.domains.signing_service import (
    create_signing_flow, add_signer, start_signing,
    query_signing_status, cancel_signing, create_flow_one_step,
    create_sign_flow_by_file
)
# 单独导入one_click_sign函数
from mcpService.domains.signing_service import one_click_sign

# 配置日志
logger = logging.getLogger(__name__)

# 创建签署域路由器
signing_router = APIRouter(
    prefix="/signing",
    tags=["签署域"],
    responses={404: {"description": "Not found"}}
)


@signing_router.post(
    "/create_signing_flow",
    summary="📝 创建签署流程",
    description="创建电子签名流程",
    operation_id="signing_create_flow"
)
@mcp_endpoint
async def create_signing_flow_endpoint(
        flow_name: str,
        document_content: str,
        environment: Optional[str] = Query(None, description="环境类型")
):
    """创建签署流程"""
    return await create_signing_flow(flow_name, document_content, environment)


@signing_router.post(
    "/add_signer",
    summary="👥 添加签署人",
    description="向签署流程添加签署人",
    operation_id="signing_add_signer"
)
@mcp_endpoint
async def add_signer_endpoint(
        flow_id: str,
        signer_name: str,
        signer_mobile: str,
        signer_idcard: str,
        environment: Optional[str] = Query(None, description="环境类型")
):
    """添加签署人"""
    return await add_signer(flow_id, signer_name, signer_mobile, signer_idcard, environment)


@signing_router.post(
    "/start_signing",
    summary="🚀 启动签署",
    description="启动签署流程",
    operation_id="signing_start"
)
@mcp_endpoint
async def start_signing_endpoint(
        flow_id: str,
        environment: Optional[str] = Query(None, description="环境类型")
):
    """启动签署流程"""
    return await start_signing(flow_id, environment)


@signing_router.post(
    "/query_signing_status",
    summary="📊 查询签署状态",
    description="查询签署流程状态",
    operation_id="signing_query_status"
)
@mcp_endpoint
async def query_signing_status_endpoint(
        flow_id: str,
        environment: Optional[str] = Query(None, description="环境类型")
):
    """查询签署状态"""
    return await query_signing_status(flow_id, environment)


@signing_router.post(
    "/cancel_signing",
    summary="❌ 取消签署",
    description="取消签署流程",
    operation_id="signing_cancel"
)
@mcp_endpoint
async def cancel_signing_endpoint(
        flow_id: str,
        reason: str = "测试取消",
        environment: Optional[str] = Query(None, description="环境类型")
):
    """取消签署流程"""
    return await cancel_signing(flow_id, reason, environment)


@signing_router.post(
    "/create_flow_one_step",
    summary="⚡ 一步创建签署流程",
    description="一步创建完整的签署流程，包含文档、签署人和签署位置信息",
    operation_id="signing_create_flow_one_step"
)
@mcp_endpoint
async def create_flow_one_step_endpoint(
        file_id: str,
        file_name: str,
        signer_account_id: str,
        authorized_account_id: str,
        business_scene: str = "一步创建流程",
        pos_page: str = "1",
        pos_x: int = 100,
        pos_y: int = 500,
        sign_type: int = 1,
        third_order_no: str = "mcp_test",
        app_id: Optional[str] = None,
        environment: Optional[str] = Query(None, description="环境类型")
):
    """
    一步创建签署流程

    参数说明:
    - file_id: 文档文件ID
    - file_name: 文档文件名
    - signer_account_id: 签署人账号ID
    - authorized_account_id: 授权账号ID
    - business_scene: 业务场景描述
    - pos_page: 签署位置页码
    - pos_x: 签署位置X坐标
    - pos_y: 签署位置Y坐标
    - sign_type: 签署类型 (1=签名)
    - third_order_no: 第三方订单号
    - app_id: 应用ID，不传则使用环境默认值
    - environment: 环境描述，支持自然语言
    """
    return await create_flow_one_step(
        file_id=file_id,
        file_name=file_name,
        signer_account_id=signer_account_id,
        authorized_account_id=authorized_account_id,
        business_scene=business_scene,
        pos_page=pos_page,
        pos_x=pos_x,
        pos_y=pos_y,
        sign_type=sign_type,
        third_order_no=third_order_no,
        app_id=app_id,
        environment=environment
    )


@signing_router.get(
    "/one_click_sign",
    summary="⚡ 一键签署",
    description="对指定流程进行一键签署操作，支持测试环境和模拟环境",
    operation_id="signing_one_click_sign"
)
@mcp_endpoint
async def one_click_sign_endpoint(
        env: str = Query(..., description="环境类型，如'测试环境'"),
        flow_id: str = Query(..., description="流程ID，如'fc7095df4d4d4977a006342c1d629aba'"),
        group: str = Query("DEFAULT", description="项目标识，默认'DEFAULT'"),
        sign_type: str = Query("SIGN", description="签署类型，默认'SIGN'")
):
    """
    一键签署

    参数说明:
    - env: 环境类型，通常为"测试环境"
    - flow_id: 要签署的流程ID
    - group: 项目标识，默认为"DEFAULT"
    - sign_type: 签署类型，默认为"SIGN"

    返回示例:
    {
      "status": "success",
      "message": "一键签署成功",
      "data": {
        "env": "测试环境",
        "flowId": "fc7095df4d4d4977a006342c1d629aba",
        "group": "DEFAULT",
        "type": "SIGN"
      }
    }
    """
    logger.info(f"一键签署请求: env={env}, flow_id={flow_id}, group={group}, type={sign_type}")

    try:
        result = await one_click_sign(
            env=env,
            flow_id=flow_id,
            group=group,
            sign_type=sign_type,
        )

        logger.info(f"一键签署完成: {result}")
        return result

    except Exception as e:
        logger.error(f"一键签署异常: {str(e)}")
        return {
            "status": "error",
            "message": f"一键签署异常: {str(e)}",
            "data": None
        }


@signing_router.get(
    "/create_sign_flow_by_file",
    summary="⚡ 通过文件创建签署流程",
    description="通过文件创建签署流程，支持测试环境和模拟环境。所有布尔参数为false时，对应的配置项不会包含在请求中",
    operation_id="create_sign_flow_by_file"
)
@mcp_endpoint
async def create_sign_flow_by_file_endpoint(
        env: str = Query(..., description="环境类型", enum=["测试环境", "模拟环境"]),
        # 主要配置开关 - 为false时对应的配置项不会包含在请求中
        attachments: bool = Query(False, description="是否包含附件配置"),
        copiers: bool = Query(False, description="是否包含抄送人配置"),
        docs: bool = Query(True, description="是否包含文档配置"),
        auth_config: bool = Query(True, description="是否包含认证配置"),
        charge_config: bool = Query(False, description="是否包含收费配置"),
        contract_config: bool = Query(False, description="是否包含合同配置"),
        notice_config: bool = Query(True, description="是否包含通知配置"),
        redirect_config: bool = Query(True, description="是否包含重定向配置"),
        sign_config: bool = Query(True, description="是否包含签署配置"),
        initiator: bool = Query(False, description="是否包含签署流程发起方"),
        签署方: str = Query("个人", description="个人签/企业签，默认个人签"),
        授权免意愿: bool = Query(True, description="授权免意愿，仅针对个人签署生效，默认开启"),
        appId: str = Query(None, description="appId")
):
    """
    通过文件创建签署流程 - 简化版MCP接口

    参数说明:
        env: 环境类型，支持"测试环境"和"模拟环境"

    ⚠️ 重要说明：
        - 当env="测试环境"时，系统会自动使用测试环境专用的默认值
        - 当env为其他值时，系统会使用通用默认值
        - 所有布尔参数为false时，对应的配置项不会包含在请求中
        - 这样可以大大简化接口参数，避免传递过多不必要的字段

    配置开关说明:
        attachments: 是否包含附件配置 (默认: False)
        copiers: 是否包含抄送人配置 (默认: False)
        docs: 是否包含文档配置 (默认: true)
        auth_config: 是否包含认证配置 (默认: true)
        charge_config: 是否包含收费配置 (默认: False)
        contract_config: 是否包含合同配置 (默认: False)
        notice_config: 是否包含通知配置 (默认: true)
        redirect_config: 是否包含重定向配置 (默认: true)
        sign_config: 是否包含签署配置 (默认: True)
        initiator: 是否包含签署流程发起方 (默认: False)
        签署方: 签署主体类型 (个人/企业，默认: 个人)

    返回示例:
    {
      "success": true,
      "message": "成功",
      "data": {
        "signFlowId": "signFlowId"
      }
    }
    """
    try:
        result = await create_sign_flow_by_file(
            env=env,
            attachments=attachments,
            copiers=copiers,
            docs=docs,
            auth_config=auth_config,
            charge_config=charge_config,
            contract_config=contract_config,
            notice_config=notice_config,
            redirect_config=redirect_config,
            sign_config=sign_config,
            initiator=initiator,
            签署方=签署方,
            appId=appId
        )

        logger.info(f"通过文件创建签署流程完成: {result}")
        return result

    except Exception as e:
        logger.error(f"通过文件创建签署流程异常: {str(e)}")
        return {
            "status": "error",
            "message": f"通过文件创建签署流程异常: {str(e)}",
            "data": None
        }


# 导出router供main.py使用
router = signing_router
