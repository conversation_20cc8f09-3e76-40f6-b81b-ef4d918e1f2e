#!/usr/bin/env python3
"""
费用域控制器 - 企业充值相关功能
提供企业配额充值等造数功能
"""
import logging
from typing import Optional

from fastapi import APIRouter, Query

from app.core.mcp_decorator import mcp_endpoint
from mcpService.domains.fee_service import add_app, recharge_quota

# 配置日志
logger = logging.getLogger(__name__)

# 创建费用域路由器
fee_router = APIRouter(
    prefix="/fee",
    tags=["费用域"],
    responses={404: {"description": "Not found"}}
)


@fee_router.get(
    "/add_app_quota",
    summary="💰 充值应用数量",
    description="为企业充值数量配额，支持模拟环境和测试环境",
    operation_id="add_app_quota"
)
@mcp_endpoint
async def add_app_quota_endpoint(
        env: str = Query(..., description="环境类型，如'模拟环境'"),
        name: Optional[str] = Query(None),
        gid: Optional[str] = Query(None)
):
    """充值应用数量"""
    return await add_app(env, name, gid)


@fee_router.get(
    "/recharge",
    summary="💰 充值账户余额",
    description="充值账户余额，支持模拟环境和测试环境",
    operation_id="fee_recharge_quota"
)
@mcp_endpoint
async def recharge_quota_endpoint(
        env: str = Query(..., description="环境类型，如'模拟环境'"),
        name: Optional[str] = Query(None),
        gid: Optional[str] = Query(None),
        appId: Optional[str] = Query(None)
):
    """充值账户余额"""
    return await recharge_quota(env, name, gid, appId)


# 导出router供main.py使用
router = fee_router
