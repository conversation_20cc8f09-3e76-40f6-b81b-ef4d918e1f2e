#!/usr/bin/env python3
"""
意愿域控制器 - 签署意愿相关功能
提供意愿确认、意愿验证等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.intention_service import (
    create_intention_verification, query_intention_status,
    confirm_intention, cancel_intention, get_intention_record
)
from app.core.mcp_decorator import mcp_endpoint

# 配置日志
logger = logging.getLogger(__name__)

# 创建意愿域路由器
intention_router = APIRouter(
    prefix="/intention",
    tags=["意愿域"],
    responses={404: {"description": "Not found"}}
)


@intention_router.post(
    "/create_verification",
    summary="✅ 创建意愿验证",
    description="创建签署意愿验证流程",
    operation_id="intention_create_verification"
)
@mcp_endpoint
async def create_intention_verification_endpoint(
    signer_name: str,
    signer_mobile: str,
    signer_idcard: str,
    document_title: str,
    verification_type: str = "SMS",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建意愿验证"""
    return await create_intention_verification(
        signer_name, signer_mobile, signer_idcard,
        document_title, verification_type, environment
    )


@intention_router.post(
    "/query_status",
    summary="📊 查询意愿状态",
    description="查询签署意愿验证状态",
    operation_id="intention_query_status"
)
@mcp_endpoint
async def query_intention_status_endpoint(
    intention_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询意愿状态"""
    return await query_intention_status(intention_id, environment)


@intention_router.post(
    "/confirm",
    summary="✔️ 确认意愿",
    description="确认签署意愿",
    operation_id="intention_confirm"
)
@mcp_endpoint
async def confirm_intention_endpoint(
    intention_id: str,
    verification_code: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """确认意愿"""
    return await confirm_intention(intention_id, verification_code, environment)


@intention_router.post(
    "/cancel",
    summary="❌ 取消意愿",
    description="取消签署意愿",
    operation_id="intention_cancel"
)
@mcp_endpoint
async def cancel_intention_endpoint(
    intention_id: str,
    reason: str = "测试取消",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """取消意愿"""
    return await cancel_intention(intention_id, reason, environment)


@intention_router.get(
    "/get_record",
    summary="📋 获取意愿记录",
    description="获取签署意愿记录",
    operation_id="intention_get_record"
)
@mcp_endpoint
async def get_intention_record_endpoint(
    signer_idcard: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    environment: Optional[str] = Query(None, description="环境类型")
):
    """获取意愿记录"""
    return await get_intention_record(
        signer_idcard, start_date, end_date, environment
    )


# 导出router供main.py使用
router = intention_router