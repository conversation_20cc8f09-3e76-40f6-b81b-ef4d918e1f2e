我是一个资深软件测试专家，为你提供了一套完整的AI生成测试用例全流程提示词，请记住并理解提示词，后续我使用快速调用指令时，你必须按照以下步骤执行：

1. 立即开始需求信息收集（如信息不完整，则自行通过需求分析进行mock补充）

2. 依次执行9个步骤的完整流程

3. 在第四步"测试用例生成及格式化"完成后，立即生成符合XMind导入标准的markdown文件

4. 文件生成完成后，继续执行后续的优化、评审、冒烟用例提取、线上验证用例提取步骤

5. 最终更新markdown文件，确保包含所有类型的测试用例

【文件生成时机】：在完成测试用例生成后立即生成文件，不等待用户要求

提示词的详细内容如下：

一、需求信息收集提

请提供以下需求信息，以便进行测试用例设计：

1. 需求基本信息

需求名称：[需求名称]

需求类型：[功能需求/性能需求/安全需求/其他]

需求优先级：[高/中/低]

2. 需求描述

业务背景：[描述需求的业务背景]

功能描述：[详细描述需求功能]

用户价值：[描述需求带来的价值]

验收标准：[描述需求的验收标准]

3. 功能范围

核心功能：[列出核心功能点]

功能模块：[列出相关功能模块]

功能依赖：[列出功能依赖关系]

功能限制：[列出功能限制条件]

4. 用户角色

主要角色：[列出主要用户角色]

角色权限：[描述各角色权限]

角色关系：[描述角色之间的关系]

特殊要求：[描述特殊角色要求]

5. 业务规则

基本规则：[列出基本业务规则]

验证规则：[列出数据验证规则]

处理规则：[列出业务处理规则]

异常规则：[列出异常处理规则]

6. 数据要求

数据实体：[列出相关数据实体]

数据关系：[描述数据之间的关系]

数据验证：[描述数据验证要求]

数据限制：[描述数据限制条件]

7. 界面要求

页面布局：[描述页面布局要求]

交互方式：[描述交互方式要求]

响应要求：[描述响应时间要求]

兼容要求：[描述兼容性要求]

8. 性能要求

响应时间：[描述响应时间要求]

并发要求：[描述并发处理要求]

资源限制：[描述资源使用限制]

性能指标：[描述具体性能指标]

9. 安全要求

权限控制：[描述权限控制要求]

数据安全：[描述数据安全要求]

访问控制：[描述访问控制要求]

安全审计：[描述安全审计要求]

10. 其他要求

特殊说明：[描述特殊说明事项]

注意事项：[描述需要注意的事项]

参考文档：[列出相关参考文档]

补充信息：[其他补充信息]

二、需求分析提示词

请基于收集的需求信息，进行需求分析，输出如下内容：

1. 功能模块：

- 核心模块：

- 辅助模块：

- 可选模块：

- 模块关系：

2. 核心功能点：

- 主要功能：

- 功能优先级：

- 功能依赖：

- 功能流程：

3. 用户角色：

- 主要用户：

- 次要用户：

- 管理用户：

- 角色交互：



4. 业务规则：

- 核心规则：

- 约束条件：

- 异常情况：

- 边界条件：

5. 数据实体：

- 主要实体：

- 实体属性：

- 实体关系：

- 数据流向：

三、测试场景分析提示词

请基于需求分析结果，结合用例设计原则、和测试用例设计覆盖，生成具备可执行、可读性高、可为维护的用例。

测试用例设计的原则
5.1、全面性
（1）应尽可能覆盖程序的各种路径。确保所有显性和隐性的需求（功能、非功能如性能/安全边界/兼容性）都有用例覆盖

（2）应考虑存在跨年、跨月的数据。 覆盖正常流程、异常流程、降级处理场景；考虑多类型数据：跨长度时间/数据、增量、存量数据处理；全新用户视角、存量客户视角；

（3）大量数据并发测试的准备

（4）不同环境因素考量，验证系统在真实物理/技术环境中的健壮性，避免“在我的机器上能运行”问题

（5）用户操作习惯覆盖，匹配真实用户行为模式，避免“理论上可用但实际难用”问题。

（6）国际化与本地化需求，确保系统在全球不同区域的语言、文化、法规下可用

5.2、正确性&明确性
（1）输入界面后的数据应与测试文档所记录的数据一致；

（2）预期结果应与测试数据发生的业务吻合。

用例目标清晰： 每个用例应有明确的测试目的（验证什么功能或场景）。

步骤清晰： 操作步骤应具体、无歧义，按顺序描述，任何测试人员都能按步骤执行。

输入数据清晰： 明确指定测试输入的具体值

预期结果清晰： 对每个操作步骤或整个用例，必须有具体、可验证、无二义的预期结果，预期结果应与测试数据发生的业务吻合。

避免模糊表述： 如“系统应正常工作”、“显示正确信息”等是无效预期

数据准确性验证深度：数据在经过不同模块处理、存储和传输后，是否会出现精度丢失、格式变化或数据不一致等问题，在测试用例中应设计相应的检查点来验证数据在整个生命周期内的准确性，确保软件对数据的处理符合业务预期。

异常数据处理的明确性：精准定义系统对异常输入的响应行为，避免模糊描述。

操作步骤的可重复性验证：任何人在符合前置条件的标准环境中执行用例，必须获得完全一致的结果。

5.3、符合正常业务惯例
（1）测试数据应符合用户实际工作业务流程。
（2）兼顾各种业务变化的可能。

5.4、系统性
（1）对于系统业务流程要能够完整说明整个系统的业务需求、系统由几个子系统组成以及它们之间的关系。
（2）对于模块业务流程要能够说明清楚子系统内部功能、重要功能点以及它们之间的关系。

5.5、连贯性
（1）对于系统业务流程来说，各个子系统之间是如何连接在一起，如果需要接口，各个子系统之间是否有正确的接口；如果是依靠页面链接，页面链接是否正确。
（2）对于模块业务流程来说，同级模块以及上下级模块是如何构成一个子系统，其内部功能接口是否连贯。

5.6、仿真性
人名、企业名称、电话号码等应具有模拟功能，符合一般的命名惯例。

5.7、可操作性
测试用例中应写清测试的操作步骤，不同的操作步骤相对应的操作结果。

5.8、可追溯性
(1) 关联需求： 每个用例应能追溯到对应的需求项，证明其存在的必要性。

(2) 覆盖范围可见： 能清晰看到哪些需求已被覆盖，哪些尚未覆盖

5.9、高效性 & 经济性
避免冗余： 精心设计，用最少的用例覆盖最多的场景和风险。

优先级排序： 根据功能重要性、使用频率、失效风险等对用例划分优先级（如：P0-核心功能/冒烟测试，P1-重要功能，P2-次要功能，P3-边缘场景），优先保障高优先级用例的覆盖和质量。

聚焦风险： 在资源有限时，优先设计覆盖高风险区域的用例。

5.10、针对性 & 适度独立性
聚焦单一功能点/场景： 一个用例最好验证一个明确的功能点或一个具体的场景（尤其是基础功能）。避免“大而全”的用例。

独立性权衡： 追求用例的独立性有利于维护和调试，但过度拆分会导致管理开销增大。需在独立性和执行效率/场景连贯性间取得平衡（流程性测试可能需要多个步骤）。

用例覆盖类型：
7.1、UI界面检查
验证界面元素是否符合设计规范，确保用户交互体验一致性

7.2、功能逻辑检查
验证业务规则和数据处理逻辑的正确性。包含：正常流程场景、单功能验证场景、异常流程场景、边界条件场景

7.3、流程验证
测试跨模块的端到端业务流程完整性。
以新/老用户视角的全流程闭环场景用例
多功能模块交叉组合场景用例

7.4、老数据处理

数据兼容性：对不同版本遗留下来的老数据的读取、解析和应用能力，确保老数据在新版本软件中能够正常加载且不会出现数据格式不兼容、数据丢失等问题，比如旧版本数据库中的历史记录在软件升级后能否准确展示和使用。

数据迁移与转换：当软件进行数据架构调整或者升级时，考查老数据向新的数据结构或存储方式迁移的准确性和完整性，以及在迁移过程中对数据进行必要转换（如数据格式转换、编码转换等）的正确性。

7.5、性能测试
评估系统在高负载下的响应能力与稳定性。包含响应时间验证、并发性能、压力测试

7.6、安全测试

身份认证与授权：检查软件是否具备完善的用户身份认证机制（如用户名密码验证、多因素认证等），不同用户角色是否被正确授权访问相应的功能和数据，防止非法用户的侵入以及越权操作。

数据加密：验证软件对用户敏感信息（如登录密码、银行卡信息等）在传输和存储过程中是否采用了有效的加密算法进行保护，避免数据泄露风险。

数据安全：验证数据在传输、存储、处理、销毁全生命周期的机密性、完整性和可用性。

接口安全：验证API接口在身份认证、数据完整性、权限控制和异常处理等方面的安全性，防御未授权访问、数据篡改、信息泄露等风险。


7.7、兼容性测试

操作系统兼容性：测试软件在不同操作系统（如 Windows、Linux、macOS 等）下的安装、运行和功能表现，确保软件在主流操作系统平台上都能正常使用，不会出现兼容性问题导致的功能异常等情况。

浏览器兼容性：针对 Web 应用，检查在不同浏览器（如 Chrome、Firefox、Safari、IE 等）及其不同版本下的页面显示、交互功能是否正常，尤其要关注一些浏览器特有的功能特性和兼容性差异对软件的影响。

硬件设备兼容性：对于一些需要特定硬件支持的软件（如手机应用与不同型号手机、智能硬件设备与不同电脑主机等），测试其在各类硬件设备上的适配情况，保证硬件的各种功能特性能够被软件正确调用和发挥作用。

第三方集成兼容：重点测试API/SDK兼容性

多端验证：PC、H5、客户端、微信小程序、支付宝小程序

7.8、业务异常

模拟用户非常规操作或业务规则冲突场景。

测试重点：逆向流程（已过期流程重新激活）；冲突操作（多人同时修改/访问同一个数据）；边界规则（证书到期后自动延期）、用户错误输入等

7.9、系统异常

验证基础设施故障时的系统容错能力

测试重点：依赖服务失效（数据库宕机/CA证书服务超时/oss文件上传系统崩溃）；资源枯竭（磁盘写满/内存泄漏/数据库自增超过上限）；网络异常（断网/高延迟）



四、测试用例生成及格式化提示

基于测试场景分析，请按照以下格式直接生成最终可用的测试用例：


4.1 测试用例格式标准

【结构要求】：

每个用例采用独立结构，使用#缩进表示层级：

TL-用例标题

PD-前置条件：前置条件1；前置条件2；

步骤一：操作描述

步骤二：操作描述

步骤三：操作描述

ER-预期结果1：预期结果描述1

2：预期结果描述2

3：预期结果描述3



MYTL-冒烟用例标题

PD-前置条件：前置条件1；前置条件2；

步骤一：操作描述

步骤二：操作描述

步骤三：操作描述

ER-预期结果1：预期结果描述1

2：预期结果描述2

3：预期结果描述3



PATL-线上验证用例标题

PD-前置条件：前置条件1；前置条件2；

步骤一：操作描述

步骤二：操作描述

步骤三：操作描述

ER-预期结果1：预期结果描述1

2：预期结果描述2

3：预期结果描述3



【格式要求】：

- 任何地方都不使用"*"号，用例标题不能使用"/"号

- 使用#缩进表示层级关系

- 每个用例完全独立，不混合在一起

- 中文数字编号（步骤一、步骤二）

- 阿拉伯数字结果编号（1、2、3、4、5）

4.3.4 测试场景全面，细致，场景用例能用长的操作链路尽可能覆盖更多的测试场景
4.3.5 覆盖所有核心功能点
4.3.6直接输出最终格式，无需后续格式化

五、测试用例优化提示词

5.1请从以下维度优化测试用例：

5.1.1 完整性检查

- 功能覆盖完整性

- 场景覆盖完整性

- 数据覆盖完整性

5.1.2 准确性检查

- 步骤描述准确性

- 期望结果准确性

- 测试数据准确性

5.1.3 可执行性检查

- 步骤可操作性

- 前置条件可达成性

- 测试环境可用性

5.1.4 可维护性检查

- 用例独立性

- 步骤清晰性

- 数据可重用性

5.2 优化后的输出

输出优化后的测试用例，保持标准格式。

六、测试用例评审及补充遗漏场景提示词

6.1 评审标准

请基于测试用例进行评审，检查：

6.1.1需求覆盖度检查

-所有功能需求已覆盖

-所有非功能需求已覆盖
- 技术架构更新，深入理解架构变更点，评估其对现有功能逻辑、性能、安全性、容错机制、稳定性的影响，并将其转化为用例；
- 针对外部接口变更可能带来的新错误场景（如新定义的错误码、限流响应），设计或更新测试用例，验证系统的容错、降级和错误处理机制是否健壮。
- 测试环境变化的适配：针对适配不同环境、不同配置、不同网络环境等情况补充相关用例
- 第三方依赖的紧急降级处理，设计/更新降级验证用例，验证开关有效性，降级功能正确性，恢复机制验证

-业务规则已体现

-异常场景已包含

-所有功能需求已有闭环验证场景覆盖

-场景交叉组合已覆盖

6.1.2 测试设计检查

- 正常流程完整

- 异常流程充分

- 边界条件合理

- 测试数据充足

6.1.3 用例质量检查

- 步骤描述清晰

- 期望结果明确

- 前置条件完整

6.1.4 格式规范检查

- 编号规范统一

- 格式符合标准

- 步骤编号正确

6.1.5缺口识别

列出发现的测试缺口：

- 功能缺口：

- 场景缺口：

- 数据缺口：

6.2 评审结果输出格式

评审结果：

1. 需求覆盖度：[百分比]

2. 场景完整性：[百分比]

3. 步骤合理性：[百分比]

4. 结果可验证性：[百分比]

5. 数据充分性：[百分比]

6. 遗漏场景：[具体场景列表]

6.3 补充测试用例

如存在遗漏场景，将遗漏场景按照标准格式生成测试用例。

七、冒烟测试用例提取提示词

请从完整测试用例中提取冒烟测试用例：

7.1 冒烟用例提取要求

覆盖核心功能点

验证基本业务流程

确保关键功能可用

用例数量控制在总用例的30%

7.2 冒烟用例输出格式

MYTL-冒烟用例标题

PD-前置条件：前置条件1；前置条件2；

步骤一：操作描述
步骤二：操作描述
步骤三：操作描述

ER-预期结果

1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

7.3 数量校验

对输出的冒烟用例进行数量检查：

公式：冒烟用例数量 = 总用例数 × 30%

示例：总用例20条，冒烟用例应为6条左右

八、线上验证用例提取提示词

请从完整测试用例中提取线上验证用例：

8.1 线上验证用例提取要求

覆盖需求发布的所有功能点

验证业务全流程可用

确保能验证到完整的功能需求

用例数量控制在总用例的30%以下



8.2 线上验证用例输出格式

线上验证用例：

PATL-线上验证用例标题

PD-前置条件：前置条件1；前置条件2；

步骤一：操作描述

步骤二：操作描述

步骤三：操作描述

ER-预期结果

1：预期结果描述1

2：预期结果描述2

3：预期结果描述3



8.3 数量校验

对输出的线上验证用例进行数量检查：

公式：线上验证用例数量 = 总用例数 × 30%

示例：总用例20条，线上验证用例应为6条左右



九、生成符合XMind导入标准的markdown文件

要求：

1. 立即生成符合XMind导入标准的markdown文件

3. 包含完整的8类测试用例，用例数量完整，

4. 重点关注[具体场景]的详细测试

5. 结构化组织原则：

- 多需求场景：每个需求作为一级分类，下设功能模块和测试类型

- 功能模块场景：功能模块作为一级分类，下设测试类型

- 混合场景：需求→功能模块→测试类型→用例标题→前置条件、操作步骤、预期结果的多级结构

- 必须保证每条用例作为单独的分支存在，不要混在一起，可读性太差满足，需求→功能模块→测试类型→用例标题→前置条件、操作步骤、预期结果的多级结构

- markdown格式参考：

# 【V3api】签署接口指定实名认证的证件类型-测试用例

## 功能测试

### 证件类型处理逻辑

#### TL-创建签署流程时psnIDCardNum为空psnIDCardType不为空透传验证

##### PD-前置条件：具有创建签署流程权限、

##### 步骤一：调用V3创建签署流程接口，设置psnIDCardNum为空；设置psnIDCardType为CRED_PSN_CH_TWCARD，提交创建请求

##### 步骤二：进入实名认证页面

##### ER-预期结果: 1：签署流程创建成功； 2：实名认证页面展示证件类型为"台湾来往大陆通行证"；3：证件类型字段不可修改；4：certType成功透传到实名认证系统；



#### TL-创建签署流程时psnIDCardNum不为空psnIDCardType为空默认值设置验证

##### PD-前置条件：用户已登录；具有创建签署流程权限；实名认证服务正常；

##### 步骤一：调用V3创建签署流程接口；设置psnIDCardNum为有效身份证号；设置psnIDCardType为空；提交创建请求

##### 步骤二：进入实名认证页面，检查系统处理结果

##### ER-预期结果：1：签署流程创建成功；2：psnIDCardType自动设置为CRED_PSN_CH_IDCARD；3：实名认证页面展示证件类型为"中华人民共和国居民身份证"；4：证件类型字段不可修改；
