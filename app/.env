# ============================================================================
# E-Sign QA MCP Platform Configuration
# 电子签名QA造数平台MCP服务配置文件
# ============================================================================

# 服务配置
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# 连接保持配置 - 针对MCP长连接优化
KEEP_ALIVE_TIMEOUT=604800
GRACEFUL_SHUTDOWN_TIMEOUT=300
MAX_CONCURRENCY=1000
MAX_REQUESTS=10000

# 环境配置
env=test
envCode=testvpc
is_mock=false

# ============================================================================
# 核心服务URL配置 - MCP底层调用的所有接口URL
# ============================================================================

# 默认基础URL - 所有接口的默认地址
base_url=http://in-test-openapi.tsign.cn

# 证书域服务URL
footstone_api_url=http://in-test-openapi.tsign.cn

# 签署域服务URL
footstone_will=http://footstone-will.testk8s.tsign.cn
footstone_willauth=http://willauth-service.testk8s.tsign.cn
footstone_template_url=http://footstone-doc.testk8s.tsign.cn
footstone_signflow_initiate_url=http://in-test-openapi.tsign.cn
flow_manager_url=http://flow-manager.testk8s.tsign.cn

# SaaS域服务URL
footstone_user_url=http://footstone-user-api.testk8s.tsign.cn
footstone_doc_url=http://footstone-doc.testk8s.tsign.cn
account_url=http://footstone-user-api.testk8s.tsign.cn
saas_common_manage_url=http://saas-common-manage.testk8s.tsign.cn

# 实名域服务URL
realname_url=http://footstone-identity.testk8s.tsign.cn

# 意愿域服务URL
footstone_mobile_shield_url=http://in-test-openapi.tsign.cn

# 其他服务URL
open_platform_url=http://open-platform-service.testk8s.tsign.cn
seal_manager_url=http://seal-manager.testk8s.tsign.cn
filesystem_service_url=http://172.20.61.187:8080
account_webserver_url=http://ttapi.tsign.cn

# ============================================================================
# 应用ID配置
# ============================================================================

# 默认应用ID - 使用BZQ-App-Id作为默认值
app_id=**********

# 证书相关应用ID
appid_cert=**********

# 其他业务应用ID（保留少量必要的）
xuanyuan_appid=**********
wukong_appid=**********

# ============================================================================
# 请求头配置
# ============================================================================

# 默认服务组
X-Tsign-Service-Group=DEFAULT

# 开放平台配置
X-Tsign-Open-App-Id=**********
X-Tsign-Open-Tenant-Id=b0f99abbc3cd4d63a5a2a84c452e52d6

# BZQ应用ID（主要使用的应用ID）
BZQ-App-Id=**********
# ============================================================================
# 测试数据配置 - 用于MCP功能测试的基础数据
# ============================================================================

# 测试账号数据
test_phone=***********
test_name=王瑶济
test_idcard=330102199001011234
test_email=<EMAIL>

# 测试组织数据
test_org_name=测试公司
test_org_code=91330100MA27XYZ123

# 常用测试ID（保留少量必要的）
test_account_id=3986d7544d9848f186ee9c5dcd342de1
test_flow_id=392c1ed88c304054bbef326795d1c08d
test_file_id=232d475467a84ce7b5c42926440a2779


# Wiki MCP Server Configuration
# 请根据实际情况配置以下参数

# Wiki服务器配置
WIKI_BASE_URL=http://wiki.timevale.cn:8081
WIKI_LOGIN_URL=https://zerotrust.esign.cn/submit

# Wiki登录凭据（二选一配置）
# 方式1：使用用户名密码登录
WIKI_USERNAME=afei
WIKI_PASSWORD=feiA12345@