2025-09-17 14:26:39,937 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:26:39,941 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:26:40,983 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:26:41,211 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:26:41,212 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '实名域', '费用域', '文件域', '意愿域', 'SaaS域', '证书域', 'wiki域', '签署域']
2025-09-17 14:26:41,212 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:26:41,212 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:26:41,213 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:26:41,219 - __main__ - INFO - certificate路由加载成功
2025-09-17 14:26:41,221 - __main__ - INFO - fee路由加载成功
2025-09-17 14:26:41,222 - __main__ - INFO - file路由加载成功
2025-09-17 14:26:41,224 - __main__ - INFO - identity路由加载成功
2025-09-17 14:26:41,229 - __main__ - INFO - intention路由加载成功
2025-09-17 14:26:41,242 - __main__ - INFO - platform路由加载成功
2025-09-17 14:26:41,253 - __main__ - INFO - saas路由加载成功
2025-09-17 14:26:41,264 - __main__ - INFO - signing路由加载成功
2025-09-17 14:26:41,267 - __main__ - INFO - wiki路由加载成功
2025-09-17 14:26:41,270 - __main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:26:41,270 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '实名域', '费用域', '文件域', '意愿域', 'SaaS域', '证书域', 'wiki域', '签署域']
2025-09-17 14:26:41,314 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:26:41,314 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:26:41,314 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:26:41,314 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:26:41,314 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-09-17 14:26:41,314 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-09-17 14:26:41,314 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:26:41,314 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-09-17 14:26:41,314 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-09-17 14:26:43,019 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:26:43,019 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:26:43,885 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:26:44,072 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:26:44,072 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', '文件域', '签署域', '证书域', '费用域', '意愿域', 'SaaS域', '平台功能']
2025-09-17 14:26:44,072 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:26:44,072 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:26:44,072 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:26:44,079 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:26:44,081 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:26:44,082 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:26:44,084 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:26:44,088 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:26:44,107 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:26:44,118 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:26:44,132 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:26:44,135 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:26:44,138 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:26:44,138 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', '文件域', '签署域', '证书域', '费用域', '意愿域', 'SaaS域', '平台功能']
2025-09-17 14:26:44,192 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:26:44,192 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:26:44,193 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:26:44,193 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:26:44,385 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:26:44,386 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:26:44,387 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', '文件域', '签署域', '证书域', '费用域', '意愿域', 'SaaS域', '平台功能']
2025-09-17 14:26:44,387 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:26:44,387 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:26:44,388 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:26:44,400 - main - INFO - certificate路由加载成功
2025-09-17 14:26:44,402 - main - INFO - fee路由加载成功
2025-09-17 14:26:44,404 - main - INFO - file路由加载成功
2025-09-17 14:26:44,407 - main - INFO - identity路由加载成功
2025-09-17 14:26:44,414 - main - INFO - intention路由加载成功
2025-09-17 14:26:44,429 - main - INFO - platform路由加载成功
2025-09-17 14:26:44,442 - main - INFO - saas路由加载成功
2025-09-17 14:26:44,458 - main - INFO - signing路由加载成功
2025-09-17 14:26:44,464 - main - INFO - wiki路由加载成功
2025-09-17 14:26:44,467 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:26:44,467 - main - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', '文件域', '签署域', '证书域', '费用域', '意愿域', 'SaaS域', '平台功能']
2025-09-17 14:26:44,566 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:26:44,566 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:26:44,566 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:26:44,566 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:26:44,567 - main - INFO - 🚀 应用启动中...
2025-09-17 14:26:44,567 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:26:44,568 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:26:44,568 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:26:44,568 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:26:44,568 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:26:44,568 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:27:26,355 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:27:26,355 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438879859", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:27:26,355 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635, "dedicatedCloudId": "1554960001"}
2025-09-17 14:27:28,476 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:27:28,477 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:27:28,477 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "5640f80091774ccea5b9292cb07d53a7", "uploadUrl": "https://lp-zsyyqxm-sml.tsign.cn/file-system/operation/upload?fileKey=QCAPJ3IXZOZZ6RFZWESMF6ZPT5U4OAAAAAAFZLWOQE&signature=PRjMZQEKLe3FbSZ2GEsHNSbpEa8%3D%0A&agentType=6"}}
2025-09-17 14:27:28,842 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'success': True, 'message': '成功', 'data': {'fileId': '5640f80091774ccea5b9292cb07d53a7', 'fileMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'appId': '4438879859', 'fileName': '3页.pdf', 'uploadStatus': 'success', 'dedicatedCloudId': '1554960001'}}
2025-09-17 14:32:17,850 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:32:17,850 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438879859", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:32:17,850 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635, "dedicatedCloudId": "1554960001"}
2025-09-17 14:32:20,445 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:32:20,446 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:32:20,446 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "64074dc5148047f8ab0e461fa2f8ba23", "uploadUrl": "https://lp-zsyyqxm-sml.tsign.cn/file-system/operation/upload?fileKey=QCABZBDAWXKUWQHWRHW7QI66SDCBIAAAAAAFZLWOQE&signature=%2FICkbFGATq23XrDG8ix3nFxb3vA%3D%0A&agentType=6"}}
2025-09-17 14:32:20,719 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'success': True, 'message': '成功', 'data': {'fileId': '64074dc5148047f8ab0e461fa2f8ba23', 'fileMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'appId': '4438879859', 'fileName': '3页.pdf', 'uploadStatus': 'success', 'dedicatedCloudId': '1554960001'}}
2025-09-17 14:32:32,800 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:32:32,800 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:32:32,801 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:32:35,018 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:32:35,020 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:32:35,020 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:32:35,025 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:32:35,026 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:32:35.025683', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:33:38,033 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:33:38,036 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:33:38,036 - main - INFO - ✅ 应用已关闭
2025-09-17 14:33:49,892 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:33:49,894 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:33:51,162 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:33:51,852 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:33:51,852 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', '签署域', 'wiki域', '文件域', '平台功能', '意愿域', 'SaaS域', '费用域', '实名域']
2025-09-17 14:33:51,853 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:33:51,853 - __main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:33:51,853 - __main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:33:51,873 - __main__ - INFO - certificate路由加载成功
2025-09-17 14:33:51,878 - __main__ - INFO - fee路由加载成功
2025-09-17 14:33:51,881 - __main__ - INFO - file路由加载成功
2025-09-17 14:33:51,887 - __main__ - INFO - identity路由加载成功
2025-09-17 14:33:51,900 - __main__ - INFO - intention路由加载成功
2025-09-17 14:33:51,932 - __main__ - INFO - platform路由加载成功
2025-09-17 14:33:51,957 - __main__ - INFO - saas路由加载成功
2025-09-17 14:33:51,987 - __main__ - INFO - signing路由加载成功
2025-09-17 14:33:51,994 - __main__ - INFO - wiki路由加载成功
2025-09-17 14:33:52,000 - __main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:33:52,001 - __main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', '签署域', 'wiki域', '文件域', '平台功能', '意愿域', 'SaaS域', '费用域', '实名域']
2025-09-17 14:33:52,150 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:33:52,150 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:33:52,150 - __main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:33:52,150 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:33:52,150 - __main__ - INFO - 🚀 启动esign-qa-mcp-platform服务
2025-09-17 14:33:52,150 - __main__ - INFO - 📚 API文档: http://localhost:8000/docs
2025-09-17 14:33:52,150 - __main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:33:52,150 - __main__ - INFO - 🔧 启动参数: host=0.0.0.0, port=8000
2025-09-17 14:33:52,150 - __main__ - INFO - 🔧 连接保持: keep_alive=604800秒, graceful_shutdown=300秒
2025-09-17 14:33:57,306 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:33:57,309 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:34:00,358 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:34:02,156 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:34:02,159 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', 'wiki域', '实名域', '证书域', '意愿域', '费用域', '平台功能', 'SaaS域', '文件域']
2025-09-17 14:34:02,160 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:34:02,161 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:34:02,161 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:34:02,194 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:34:02,200 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:34:02,204 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:34:02,212 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:34:02,280 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:34:02,334 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:34:02,382 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:34:02,510 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:34:02,521 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:34:02,537 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:34:02,537 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['签署域', 'wiki域', '实名域', '证书域', '意愿域', '费用域', '平台功能', 'SaaS域', '文件域']
2025-09-17 14:34:02,837 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:34:02,838 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:34:02,839 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:34:02,839 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:34:04,502 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:34:04,506 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:34:04,507 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['签署域', 'wiki域', '实名域', '证书域', '意愿域', '费用域', '平台功能', 'SaaS域', '文件域']
2025-09-17 14:34:04,508 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:34:04,509 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:34:04,510 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:34:04,591 - main - INFO - certificate路由加载成功
2025-09-17 14:34:04,622 - main - INFO - fee路由加载成功
2025-09-17 14:34:04,634 - main - INFO - file路由加载成功
2025-09-17 14:34:04,656 - main - INFO - identity路由加载成功
2025-09-17 14:34:04,713 - main - INFO - intention路由加载成功
2025-09-17 14:34:04,869 - main - INFO - platform路由加载成功
2025-09-17 14:34:04,990 - main - INFO - saas路由加载成功
2025-09-17 14:34:05,136 - main - INFO - signing路由加载成功
2025-09-17 14:34:05,157 - main - INFO - wiki路由加载成功
2025-09-17 14:34:05,172 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:34:05,172 - main - INFO - 🏷️ 使用自动发现的标签: ['签署域', 'wiki域', '实名域', '证书域', '意愿域', '费用域', '平台功能', 'SaaS域', '文件域']
2025-09-17 14:34:05,629 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:34:05,630 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:34:05,631 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:34:05,631 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:34:05,638 - main - INFO - 🚀 应用启动中...
2025-09-17 14:34:05,639 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:34:05,640 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:34:05,641 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:34:05,641 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:34:05,641 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:34:05,642 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:34:17,606 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:34:17,606 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:34:17,607 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:34:20,091 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:34:20,096 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:34:20,096 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:34:57,279 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:34:57,279 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:34:57.279063', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:35:05,977 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:35:05,978 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:35:05,978 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:35:08,767 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:35:08,770 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:35:08,770 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:35:08,773 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:35:08,773 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:35:08.773236', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:35:08,811 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:35:08,813 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:35:08,813 - main - INFO - ✅ 应用已关闭
2025-09-17 14:35:14,914 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:35:14,917 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:35:16,409 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:35:16,996 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:35:16,997 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '证书域', 'SaaS域', '文件域', '意愿域', '实名域', '签署域', '费用域', 'wiki域']
2025-09-17 14:35:16,997 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:35:16,997 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:35:16,997 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:35:17,018 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:35:17,022 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:35:17,026 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:35:17,032 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:35:17,047 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:35:17,080 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:35:17,106 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:35:17,137 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:35:17,143 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:35:17,149 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:35:17,149 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '证书域', 'SaaS域', '文件域', '意愿域', '实名域', '签署域', '费用域', 'wiki域']
2025-09-17 14:35:17,313 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:35:17,313 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:35:17,314 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:35:17,314 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:35:17,472 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:35:17,473 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:35:17,474 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['平台功能', '证书域', 'SaaS域', '文件域', '意愿域', '实名域', '签署域', '费用域', 'wiki域']
2025-09-17 14:35:17,474 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:35:17,474 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:35:17,474 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:35:17,498 - main - INFO - certificate路由加载成功
2025-09-17 14:35:17,507 - main - INFO - fee路由加载成功
2025-09-17 14:35:17,513 - main - INFO - file路由加载成功
2025-09-17 14:35:17,524 - main - INFO - identity路由加载成功
2025-09-17 14:35:17,548 - main - INFO - intention路由加载成功
2025-09-17 14:35:17,595 - main - INFO - platform路由加载成功
2025-09-17 14:35:17,628 - main - INFO - saas路由加载成功
2025-09-17 14:35:17,664 - main - INFO - signing路由加载成功
2025-09-17 14:35:17,672 - main - INFO - wiki路由加载成功
2025-09-17 14:35:17,676 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:35:17,676 - main - INFO - 🏷️ 使用自动发现的标签: ['平台功能', '证书域', 'SaaS域', '文件域', '意愿域', '实名域', '签署域', '费用域', 'wiki域']
2025-09-17 14:35:17,862 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:35:17,863 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:35:17,863 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:35:17,863 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:35:17,866 - main - INFO - 🚀 应用启动中...
2025-09-17 14:35:17,867 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:35:17,868 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:35:17,868 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:35:17,868 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:35:17,868 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:35:17,868 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:35:23,914 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:35:23,915 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:35:23,916 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:35:26,663 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:35:26,667 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:35:26,667 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:35:38,716 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:35:38,716 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:35:38.716982', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:37:47,265 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:37:47,265 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:37:47,266 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:37:49,199 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:37:49,205 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:37:49,206 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:38:12,709 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:38:12,710 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:38:12.709145', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:38:29,763 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:38:29,763 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:38:29,763 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:38:32,036 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:38:32,037 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:38:32,038 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:38:34,664 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:38:34,664 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:38:34.664469', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:38:34,781 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:38:34,783 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:38:34,783 - main - INFO - ✅ 应用已关闭
2025-09-17 14:38:38,886 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:38:38,888 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:38:39,970 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:38:40,542 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:38:40,542 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '签署域', '证书域', 'SaaS域', 'wiki域', '意愿域', '费用域', '平台功能', '文件域']
2025-09-17 14:38:40,543 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:38:40,543 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:38:40,543 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:38:40,563 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:38:40,568 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:38:40,572 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:38:40,577 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:38:40,592 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:38:40,625 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:38:40,649 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:38:40,678 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:38:40,683 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:38:40,689 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:38:40,690 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', '签署域', '证书域', 'SaaS域', 'wiki域', '意愿域', '费用域', '平台功能', '文件域']
2025-09-17 14:38:40,809 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:38:40,810 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:38:40,810 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:38:40,810 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:38:40,931 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:38:40,932 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:38:40,933 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', '签署域', '证书域', 'SaaS域', 'wiki域', '意愿域', '费用域', '平台功能', '文件域']
2025-09-17 14:38:40,933 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:38:40,933 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:38:40,933 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:38:40,953 - main - INFO - certificate路由加载成功
2025-09-17 14:38:40,958 - main - INFO - fee路由加载成功
2025-09-17 14:38:40,962 - main - INFO - file路由加载成功
2025-09-17 14:38:40,966 - main - INFO - identity路由加载成功
2025-09-17 14:38:40,980 - main - INFO - intention路由加载成功
2025-09-17 14:38:41,011 - main - INFO - platform路由加载成功
2025-09-17 14:38:41,037 - main - INFO - saas路由加载成功
2025-09-17 14:38:41,069 - main - INFO - signing路由加载成功
2025-09-17 14:38:41,074 - main - INFO - wiki路由加载成功
2025-09-17 14:38:41,078 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:38:41,078 - main - INFO - 🏷️ 使用自动发现的标签: ['实名域', '签署域', '证书域', 'SaaS域', 'wiki域', '意愿域', '费用域', '平台功能', '文件域']
2025-09-17 14:38:41,211 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:38:41,211 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:38:41,211 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:38:41,212 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:38:41,215 - main - INFO - 🚀 应用启动中...
2025-09-17 14:38:41,215 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:38:41,216 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:38:41,216 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:38:41,216 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:38:41,216 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:38:41,216 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:38:43,712 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:38:43,712 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:38:43,713 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:38:47,731 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:38:47,738 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:38:47,738 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:38:49,543 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:38:49,543 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:38:49.543177', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:39:09,706 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:39:09,707 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:39:09,707 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:39:12,773 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:39:12,775 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:39:12,775 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:39:15,800 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:39:15,800 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:39:15.800620', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:39:15,806 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:39:15,810 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:39:15,810 - main - INFO - ✅ 应用已关闭
2025-09-17 14:39:19,991 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:39:19,993 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:39:21,102 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:39:21,825 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:39:21,826 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '意愿域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '文件域', '证书域']
2025-09-17 14:39:21,826 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:39:21,826 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:39:21,826 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:39:21,852 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:39:21,859 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:39:21,863 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:39:21,872 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:39:21,890 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:39:21,929 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:39:21,963 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:39:22,001 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:39:22,009 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:39:22,017 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:39:22,017 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['费用域', '意愿域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '文件域', '证书域']
2025-09-17 14:39:22,233 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:39:22,234 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:39:22,234 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:39:22,234 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:39:22,418 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:39:22,419 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:39:22,420 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '意愿域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '文件域', '证书域']
2025-09-17 14:39:22,420 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:39:22,420 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:39:22,421 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:39:22,451 - main - INFO - certificate路由加载成功
2025-09-17 14:39:22,461 - main - INFO - fee路由加载成功
2025-09-17 14:39:22,466 - main - INFO - file路由加载成功
2025-09-17 14:39:22,476 - main - INFO - identity路由加载成功
2025-09-17 14:39:22,502 - main - INFO - intention路由加载成功
2025-09-17 14:39:22,561 - main - INFO - platform路由加载成功
2025-09-17 14:39:22,601 - main - INFO - saas路由加载成功
2025-09-17 14:39:22,646 - main - INFO - signing路由加载成功
2025-09-17 14:39:22,657 - main - INFO - wiki路由加载成功
2025-09-17 14:39:22,667 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:39:22,667 - main - INFO - 🏷️ 使用自动发现的标签: ['费用域', '意愿域', '签署域', '实名域', 'SaaS域', 'wiki域', '平台功能', '文件域', '证书域']
2025-09-17 14:39:22,918 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:39:22,919 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:39:22,919 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:39:22,919 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:39:22,923 - main - INFO - 🚀 应用启动中...
2025-09-17 14:39:22,924 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:39:22,925 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:39:22,926 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:39:22,926 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:39:22,926 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:39:22,927 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:39:25,620 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:39:25,621 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:39:25,622 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:39:29,370 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:39:29,373 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:39:29,374 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:39:32,330 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:39:29.376616', 'details': {'success': False, 'code': 401, 'message': '无效的应用', 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:39:29 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:39:29 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}
2025-09-17 14:39:43,928 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:39:43,928 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:39:43,928 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:39:46,451 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:39:46,453 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:39:46,454 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:39:56,369 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:39:46.457027', 'details': {'success': False, 'code': 401, 'message': '无效的应用', 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:39:46 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:39:46 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}
2025-09-17 14:40:00,244 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:40:00,244 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:40:00,245 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:40:04,238 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:40:04,240 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:40:04,241 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:40:08,486 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:40:04.243510', 'details': {'success': False, 'code': 401, 'message': '无效的应用', 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:40:04 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}, 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:40:04 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}
2025-09-17 14:40:11,547 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:40:11,549 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:40:11,549 - main - INFO - ✅ 应用已关闭
2025-09-17 14:40:20,057 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:40:20,059 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:40:21,404 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:40:22,497 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:40:22,498 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', 'SaaS域', '签署域', '文件域', '意愿域', '费用域', '平台功能', 'wiki域', '实名域']
2025-09-17 14:40:22,499 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:40:22,499 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:40:22,499 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:40:22,521 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:40:22,528 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:40:22,532 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:40:22,542 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:40:22,561 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:40:22,606 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:40:22,641 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:40:22,719 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:40:22,731 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:40:22,745 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:40:22,745 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['证书域', 'SaaS域', '签署域', '文件域', '意愿域', '费用域', '平台功能', 'wiki域', '实名域']
2025-09-17 14:40:22,939 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:40:22,939 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:40:22,939 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:40:22,939 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:40:23,087 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:40:23,088 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:40:23,088 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['证书域', 'SaaS域', '签署域', '文件域', '意愿域', '费用域', '平台功能', 'wiki域', '实名域']
2025-09-17 14:40:23,089 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:40:23,089 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:40:23,089 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:40:23,110 - main - INFO - certificate路由加载成功
2025-09-17 14:40:23,117 - main - INFO - fee路由加载成功
2025-09-17 14:40:23,120 - main - INFO - file路由加载成功
2025-09-17 14:40:23,126 - main - INFO - identity路由加载成功
2025-09-17 14:40:23,146 - main - INFO - intention路由加载成功
2025-09-17 14:40:23,202 - main - INFO - platform路由加载成功
2025-09-17 14:40:23,255 - main - INFO - saas路由加载成功
2025-09-17 14:40:23,603 - main - INFO - signing路由加载成功
2025-09-17 14:40:23,624 - main - INFO - wiki路由加载成功
2025-09-17 14:40:23,634 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:40:23,634 - main - INFO - 🏷️ 使用自动发现的标签: ['证书域', 'SaaS域', '签署域', '文件域', '意愿域', '费用域', '平台功能', 'wiki域', '实名域']
2025-09-17 14:40:23,950 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:40:23,950 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:40:23,951 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:40:23,951 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:40:23,957 - main - INFO - 🚀 应用启动中...
2025-09-17 14:40:23,958 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:40:23,958 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:40:23,960 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:40:23,960 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:40:23,960 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:40:23,960 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:40:24,014 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:40:24,014 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:40:24,015 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:40:26,530 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:40:26,534 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:40:26,534 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:40:28,984 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:40:28,985 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:40:28.984605', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:40:37,254 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:40:37,255 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:40:37,256 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:40:39,988 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:40:39,992 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:40:39,993 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:41:18,716 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:41:18,718 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:41:18.717376', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:41:31,975 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:41:31,975 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:41:31,975 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:41:34,481 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:41:34,484 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:41:45,483 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:47:28,479 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:47:28,480 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:47:28.479866', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:47:28,603 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:47:28,605 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:47:28,605 - main - INFO - ✅ 应用已关闭
2025-09-17 14:47:36,738 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:47:36,741 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:47:38,530 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:47:39,220 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:47:39,220 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', 'wiki域', '证书域', 'SaaS域', '文件域', '签署域', '平台功能', '费用域', '实名域']
2025-09-17 14:47:39,221 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:47:39,221 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:47:39,221 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:47:39,243 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:47:39,248 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:47:39,252 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:47:39,259 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:47:39,276 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:47:39,313 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:47:39,344 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:47:39,379 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:47:39,385 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:47:39,391 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:47:39,392 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['意愿域', 'wiki域', '证书域', 'SaaS域', '文件域', '签署域', '平台功能', '费用域', '实名域']
2025-09-17 14:47:39,555 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:47:39,555 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:47:39,556 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:47:39,556 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:47:39,687 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:47:39,688 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:47:39,688 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['意愿域', 'wiki域', '证书域', 'SaaS域', '文件域', '签署域', '平台功能', '费用域', '实名域']
2025-09-17 14:47:39,689 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:47:39,689 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:47:39,689 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:47:39,710 - main - INFO - certificate路由加载成功
2025-09-17 14:47:39,716 - main - INFO - fee路由加载成功
2025-09-17 14:47:39,719 - main - INFO - file路由加载成功
2025-09-17 14:47:39,725 - main - INFO - identity路由加载成功
2025-09-17 14:47:39,740 - main - INFO - intention路由加载成功
2025-09-17 14:47:39,786 - main - INFO - platform路由加载成功
2025-09-17 14:47:39,836 - main - INFO - saas路由加载成功
2025-09-17 14:47:39,878 - main - INFO - signing路由加载成功
2025-09-17 14:47:39,884 - main - INFO - wiki路由加载成功
2025-09-17 14:47:39,890 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:47:39,890 - main - INFO - 🏷️ 使用自动发现的标签: ['意愿域', 'wiki域', '证书域', 'SaaS域', '文件域', '签署域', '平台功能', '费用域', '实名域']
2025-09-17 14:47:40,035 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:47:40,036 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:47:40,036 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:47:40,036 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:47:40,040 - main - INFO - 🚀 应用启动中...
2025-09-17 14:47:40,041 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:47:40,041 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:47:40,041 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:47:40,042 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:47:40,042 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:47:40,043 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:47:40,084 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:47:40,084 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:47:40,085 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:47:43,048 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:47:43,053 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:47:46,178 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:48:03,136 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:48:03,141 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:48:03.136124', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:48:10,298 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:48:10,298 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:48:10,299 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:48:12,982 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:48:12,986 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:48:12,986 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:48:14,306 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'data'
2025-09-17 14:48:14,306 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'data'", 'timestamp': '2025-09-17T14:48:14.306338', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:48:14,410 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:48:14,415 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:48:14,415 - main - INFO - ✅ 应用已关闭
2025-09-17 14:48:20,823 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:48:20,827 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:48:22,397 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:48:23,089 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:48:23,089 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '实名域', 'SaaS域', 'wiki域', '意愿域', '签署域', '平台功能', '证书域', '文件域']
2025-09-17 14:48:23,090 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:48:23,090 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:48:23,091 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:48:23,115 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:48:23,120 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:48:23,124 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:48:23,130 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:48:23,153 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:48:23,191 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:48:23,221 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:48:23,255 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:48:23,261 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:48:23,268 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:48:23,268 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['费用域', '实名域', 'SaaS域', 'wiki域', '意愿域', '签署域', '平台功能', '证书域', '文件域']
2025-09-17 14:48:23,456 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:48:23,457 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:48:23,457 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:48:23,457 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:48:23,608 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:48:23,609 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:48:23,609 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['费用域', '实名域', 'SaaS域', 'wiki域', '意愿域', '签署域', '平台功能', '证书域', '文件域']
2025-09-17 14:48:23,609 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:48:23,610 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:48:23,610 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:48:23,631 - main - INFO - certificate路由加载成功
2025-09-17 14:48:23,637 - main - INFO - fee路由加载成功
2025-09-17 14:48:23,641 - main - INFO - file路由加载成功
2025-09-17 14:48:23,647 - main - INFO - identity路由加载成功
2025-09-17 14:48:23,666 - main - INFO - intention路由加载成功
2025-09-17 14:48:23,730 - main - INFO - platform路由加载成功
2025-09-17 14:48:23,776 - main - INFO - saas路由加载成功
2025-09-17 14:48:23,812 - main - INFO - signing路由加载成功
2025-09-17 14:48:23,819 - main - INFO - wiki路由加载成功
2025-09-17 14:48:23,824 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:48:23,824 - main - INFO - 🏷️ 使用自动发现的标签: ['费用域', '实名域', 'SaaS域', 'wiki域', '意愿域', '签署域', '平台功能', '证书域', '文件域']
2025-09-17 14:48:23,987 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:48:23,988 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:48:23,988 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:48:23,988 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:48:23,991 - main - INFO - 🚀 应用启动中...
2025-09-17 14:48:23,992 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:48:23,992 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:48:23,993 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:48:23,993 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:48:23,993 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:48:23,993 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:48:24,026 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:48:24,026 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:48:24,027 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:48:26,489 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:48:26,497 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:48:26,497 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:48:30,066 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:48:26.502258'}
2025-09-17 14:48:36,751 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:48:36,751 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:48:36,751 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:48:38,588 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:48:38,589 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:48:38,590 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:48:47,866 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:48:38.592020'}
2025-09-17 14:48:50,451 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:48:50,451 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:48:50,451 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:48:52,279 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:48:52,280 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:48:52,281 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:48:56,361 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': 'HTTP错误: 401', 'timestamp': '2025-09-17T14:48:52.283063'}
2025-09-17 14:48:56,473 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:48:56,476 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:48:56,476 - main - INFO - ✅ 应用已关闭
2025-09-17 14:49:03,822 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:49:03,823 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:49:05,039 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:49:05,635 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:49:05,636 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', 'SaaS域', '平台功能', '费用域', '签署域', '文件域', '证书域', '意愿域']
2025-09-17 14:49:05,636 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:49:05,636 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:49:05,637 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:49:05,657 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:49:05,662 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:49:05,665 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:49:05,671 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:49:05,686 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:49:05,716 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:49:05,741 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:49:05,774 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:49:05,779 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:49:05,785 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:49:05,785 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', 'SaaS域', '平台功能', '费用域', '签署域', '文件域', '证书域', '意愿域']
2025-09-17 14:49:05,928 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:49:05,928 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:49:05,928 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:49:05,929 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:49:06,052 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:49:06,053 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:49:06,054 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['实名域', 'wiki域', 'SaaS域', '平台功能', '费用域', '签署域', '文件域', '证书域', '意愿域']
2025-09-17 14:49:06,054 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:49:06,054 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:49:06,054 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:49:06,075 - main - INFO - certificate路由加载成功
2025-09-17 14:49:06,080 - main - INFO - fee路由加载成功
2025-09-17 14:49:06,083 - main - INFO - file路由加载成功
2025-09-17 14:49:06,089 - main - INFO - identity路由加载成功
2025-09-17 14:49:06,103 - main - INFO - intention路由加载成功
2025-09-17 14:49:06,137 - main - INFO - platform路由加载成功
2025-09-17 14:49:06,168 - main - INFO - saas路由加载成功
2025-09-17 14:49:06,222 - main - INFO - signing路由加载成功
2025-09-17 14:49:06,232 - main - INFO - wiki路由加载成功
2025-09-17 14:49:06,239 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:49:06,239 - main - INFO - 🏷️ 使用自动发现的标签: ['实名域', 'wiki域', 'SaaS域', '平台功能', '费用域', '签署域', '文件域', '证书域', '意愿域']
2025-09-17 14:49:06,394 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:49:06,394 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:49:06,395 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:49:06,395 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:49:06,399 - main - INFO - 🚀 应用启动中...
2025-09-17 14:49:06,399 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:49:06,400 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:49:06,400 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:49:06,401 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:49:06,401 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:49:06,401 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:49:06,432 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:49:06,432 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:49:06,432 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:49:10,768 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:49:10,778 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:49:10,779 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:49:12,582 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'success': False, 'code': 401, 'message': '无效的应用', 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:49:10 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}
2025-09-17 14:49:28,293 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:49:28,293 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438869840", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:49:28,294 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:49:30,963 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:49:30,968 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:49:30,969 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "47c3e8f1c4e446cfbb94efbb5aaa32c5", "uploadUrl": "https://esignoss.esign.cn/1111564182/97f5f1a6-924d-45a7-91ad-d512b5fccdba/3%E9%A1%B5.pdf?Expires=1758095371&OSSAccessKeyId=STS.NZmhUr1jytt95gxNamMZshRkY&Signature=fsnIctvd1ntcVYyO9LvE8j%2BSJgY%3D&callback-var=eyJ4OmZpbGVfa2V5IjoiJDM1MDRmMTA3LTNkYTktNDliMi04MzhlLWUyMjg5M2E4MjY3NyQxNzc1ODU1MDQwIn0%3D%0A&callback=eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9zbWx0YXBpLnRzaWduLmNuL2FueWRvb3IvZmlsZS1zeXN0ZW0vY2FsbGJhY2svYWxpb3NzIiwiY2FsbGJhY2tCb2R5IjogIntcIm1pbWVUeXBlXCI6JHttaW1lVHlwZX0sXCJzaXplXCI6ICR7c2l6ZX0sXCJidWNrZXRcIjogJHtidWNrZXR9LFwib2JqZWN0XCI6ICR7b2JqZWN0fSxcImV0YWdcIjogJHtldGFnfSxcImZpbGVfa2V5XCI6JHt4OmZpbGVfa2V5fX0iLCJjYWxsYmFja0JvZHlUeXBlIjogImFwcGxpY2F0aW9uL2pzb24ifQ%3D%3D%0A&security-token=CAISxAJ1q6Ft5B2yfSjIr5nYI%2B%2FG3LVYw7bSN0HJqmE4QdVfh5fAuzz2IHtKdXRvBu8Xs%2F4wnmxX7f4YlqB6T55OSAmcNZEod1zfL%2F76MeT7oMWQweEurv%2FMQBqyaXPS2MvVfJ%2BOLrf0ceusbFbpjzJ6xaCAGxypQ12iN%2B%2Fm6%2FNgdc9FHHPPD1x8CcxROxFppeIDKHLVLozNCBPxhXfKB0ca0WgVy0EHsPnvm5DNs0uH1AKjkbRM9r6ceMb0M5NeW75kSMqw0eBMca7M7TVd8RAi9t0t1%2FIVpGiY4YDAWQYLv0rda7DOltFiMkpla7MmXqlft%2BhzcgeQY0pc%2FW6e6mGuXYk9O0y3LOgrUFvAtW1omPyN1KcpSpXcU%2B3%2Bp1TPyXcAS1g12v00dZTvxLH%2Bh%2BJCYs8X58VMgjO1iX9IIPPtZRAi98Qdpz0agAGSdfeawn%2F3WJnsawWBDkNu%2FURi%2FLtvD8dl3HP7KQSvorIwAA8aP3ixVHBv75HAtcN3fXschOPDxJvIJrukOgHxT07Y8iKbSLOw96Lkt8cgySyFx4zZBdmZsYRD1xvQtMIqa%2FNXAz8hnU2OT2JSnBGgxG0Ty6UhqMQeIw6lQ%2FfVxSAA"}}
2025-09-17 14:49:35,760 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'details'
2025-09-17 14:49:35,760 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'details'", 'timestamp': '2025-09-17T14:49:35.760932', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:49:37,587 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:49:37,587 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438869840", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:49:37,588 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:49:40,686 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:49:40,688 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:49:40,688 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "caf53a550b1e4bd5841c288007a21807", "uploadUrl": "https://esignoss.esign.cn/1111564182/64ec9e48-7a28-4e99-a3ee-9db9d076d8e1/3%E9%A1%B5.pdf?Expires=1758095380&OSSAccessKeyId=STS.NYuP5iyKPSA57wk3UxnXd7CJW&Signature=Yz9Vr9wLm2%2F%2BhyvwfvvwsozLDfU%3D&callback-var=eyJ4OmZpbGVfa2V5IjoiJGY1NWJmMWIyLWRhYmUtNGIyZC04MGJjLWMyZGNmZGNlMWE0NyQzNzA5Mzc2OTkxIn0%3D%0A&callback=eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9zbWx0YXBpLnRzaWduLmNuL2FueWRvb3IvZmlsZS1zeXN0ZW0vY2FsbGJhY2svYWxpb3NzIiwiY2FsbGJhY2tCb2R5IjogIntcIm1pbWVUeXBlXCI6JHttaW1lVHlwZX0sXCJzaXplXCI6ICR7c2l6ZX0sXCJidWNrZXRcIjogJHtidWNrZXR9LFwib2JqZWN0XCI6ICR7b2JqZWN0fSxcImV0YWdcIjogJHtldGFnfSxcImZpbGVfa2V5XCI6JHt4OmZpbGVfa2V5fX0iLCJjYWxsYmFja0JvZHlUeXBlIjogImFwcGxpY2F0aW9uL2pzb24ifQ%3D%3D%0A&security-token=CAISxAJ1q6Ft5B2yfSjIr5rAG4%2FdlJRx5IPeNVHa11UtYtdI2IbhtTz2IHtKdXRvBu8Xs%2F4wnmxX7f4YlqB6T55OSAmcNZEoQ2TkL%2F76MeT7oMWQweEurv%2FMQBqyaXPS2MvVfJ%2BOLrf0ceusbFbpjzJ6xaCAGxypQ12iN%2B%2Fm6%2FNgdc9FHHPPD1x8CcxROxFppeIDKHLVLozNCBPxhXfKB0ca0WgVy0EHsPnvm5DNs0uH1AKjkbRM9r6ceMb0M5NeW75kSMqw0eBMca7M7TVd8RAi9t0t1%2FIVpGiY4YDAWQYLv0rda7DOltFiMkpla7MmXqlft%2BhzcgeQY0pc%2FW6e6mGuXYk9O0y3LOgrkNnDgW1omPyN1KcpSpXcU%2B3%2Bp1TPyXcAS1g12v00dZTvxLH%2Bh%2BJCYs8X58VMgjO1iX9IIPPtIevraMQdpz0agAFEKOsZF7YPqSjixE9tFD9GVj0eWRIvfWKeZwOEhrynC7%2FePyjUCQlFnLcy55sMxjKN6x6fk1DqciC0XNhlYUtzWZCpsf6NYvQhO2oy2P1JyJ9Tp%2FM%2FJYGP%2FvvtfjKuzAAmVKSWA2W%2FtFqwNn2SSObvmk3zUnfjFQ1jKtwWKc1T3yAA"}}
2025-09-17 14:50:13,202 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'details'
2025-09-17 14:50:13,203 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'details'", 'timestamp': '2025-09-17T14:50:13.202637', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:50:21,357 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:50:21,358 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438869840", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:50:21,358 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:50:24,720 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:50:24,725 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:50:24,726 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "dfffb41711c64bb5ba7035ceddc86341", "uploadUrl": "https://esignoss.esign.cn/1111564182/a4bff4ee-f291-47f3-ba53-d415f94019ff/3%E9%A1%B5.pdf?Expires=1758095424&OSSAccessKeyId=STS.NZmhUr1jytt95gxNamMZshRkY&Signature=KF2NGYtCoSwvFaQ7Wltjzz%2FDrEY%3D&callback-var=eyJ4OmZpbGVfa2V5IjoiJDIzMDcyNTcwLTkxZmMtNGFlMC05ZjJjLWYzOWNjNGIwNzVhYSQzNjQwNzQwOTg5In0%3D%0A&callback=eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9zbWx0YXBpLnRzaWduLmNuL2FueWRvb3IvZmlsZS1zeXN0ZW0vY2FsbGJhY2svYWxpb3NzIiwiY2FsbGJhY2tCb2R5IjogIntcIm1pbWVUeXBlXCI6JHttaW1lVHlwZX0sXCJzaXplXCI6ICR7c2l6ZX0sXCJidWNrZXRcIjogJHtidWNrZXR9LFwib2JqZWN0XCI6ICR7b2JqZWN0fSxcImV0YWdcIjogJHtldGFnfSxcImZpbGVfa2V5XCI6JHt4OmZpbGVfa2V5fX0iLCJjYWxsYmFja0JvZHlUeXBlIjogImFwcGxpY2F0aW9uL2pzb24ifQ%3D%3D%0A&security-token=CAISxAJ1q6Ft5B2yfSjIr5nYI%2B%2FG3LVYw7bSN0HJqmE4QdVfh5fAuzz2IHtKdXRvBu8Xs%2F4wnmxX7f4YlqB6T55OSAmcNZEod1zfL%2F76MeT7oMWQweEurv%2FMQBqyaXPS2MvVfJ%2BOLrf0ceusbFbpjzJ6xaCAGxypQ12iN%2B%2Fm6%2FNgdc9FHHPPD1x8CcxROxFppeIDKHLVLozNCBPxhXfKB0ca0WgVy0EHsPnvm5DNs0uH1AKjkbRM9r6ceMb0M5NeW75kSMqw0eBMca7M7TVd8RAi9t0t1%2FIVpGiY4YDAWQYLv0rda7DOltFiMkpla7MmXqlft%2BhzcgeQY0pc%2FW6e6mGuXYk9O0y3LOgrUFvAtW1omPyN1KcpSpXcU%2B3%2Bp1TPyXcAS1g12v00dZTvxLH%2Bh%2BJCYs8X58VMgjO1iX9IIPPtZRAi98Qdpz0agAGSdfeawn%2F3WJnsawWBDkNu%2FURi%2FLtvD8dl3HP7KQSvorIwAA8aP3ixVHBv75HAtcN3fXschOPDxJvIJrukOgHxT07Y8iKbSLOw96Lkt8cgySyFx4zZBdmZsYRD1xvQtMIqa%2FNXAz8hnU2OT2JSnBGgxG0Ty6UhqMQeIw6lQ%2FfVxSAA"}}
2025-09-17 14:50:27,109 - mcpService.domains.file_service - ERROR - 获取文件id异常: 'details'
2025-09-17 14:50:27,109 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'status': 'error', 'message': "获取文件id异常: 'details'", 'timestamp': '2025-09-17T14:50:27.109415', 'details': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}}
2025-09-17 14:50:27,221 - main - INFO - 🛑 应用关闭中...
2025-09-17 14:50:27,223 - main - INFO - ✅ 连接状态已保存
2025-09-17 14:50:27,223 - main - INFO - ✅ 应用已关闭
2025-09-17 14:50:33,802 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:50:33,804 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:50:35,001 - app.core.call_stats - INFO - ✅ Redis连接成功
2025-09-17 14:50:35,568 - app.mcpController.domains - INFO - 🔍 自动发现并导入了 9 个路由器
2025-09-17 14:50:35,569 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '费用域', '实名域', '文件域', '签署域', 'wiki域', '意愿域', '平台功能', '证书域']
2025-09-17 14:50:35,569 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:50:35,569 - __mp_main__ - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:50:35,570 - __mp_main__ - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:50:35,590 - __mp_main__ - INFO - certificate路由加载成功
2025-09-17 14:50:35,595 - __mp_main__ - INFO - fee路由加载成功
2025-09-17 14:50:35,598 - __mp_main__ - INFO - file路由加载成功
2025-09-17 14:50:35,605 - __mp_main__ - INFO - identity路由加载成功
2025-09-17 14:50:35,619 - __mp_main__ - INFO - intention路由加载成功
2025-09-17 14:50:35,654 - __mp_main__ - INFO - platform路由加载成功
2025-09-17 14:50:35,679 - __mp_main__ - INFO - saas路由加载成功
2025-09-17 14:50:35,710 - __mp_main__ - INFO - signing路由加载成功
2025-09-17 14:50:35,715 - __mp_main__ - INFO - wiki路由加载成功
2025-09-17 14:50:35,721 - __mp_main__ - INFO - 🚀 设置MCP服务...
2025-09-17 14:50:35,721 - __mp_main__ - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '费用域', '实名域', '文件域', '签署域', 'wiki域', '意愿域', '平台功能', '证书域']
2025-09-17 14:50:35,874 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:50:35,875 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:50:35,875 - __mp_main__ - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:50:35,875 - __mp_main__ - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:50:35,997 - app.core.auto_discovery - INFO - 开始自动发现...
2025-09-17 14:50:35,998 - app.core.auto_discovery - INFO - 发现 9 个控制器模块
2025-09-17 14:50:35,999 - app.core.auto_discovery - INFO - 发现 9 个路由标签: ['SaaS域', '费用域', '实名域', '文件域', '签署域', 'wiki域', '意愿域', '平台功能', '证书域']
2025-09-17 14:50:35,999 - app.core.auto_discovery - INFO - 自动发现完成: {'total_modules': 9, 'total_tags': 9, 'successful_modules': 9}
2025-09-17 14:50:35,999 - main - INFO - 🔍 自动发现了 9 个控制器模块
2025-09-17 14:50:35,999 - main - INFO - 🏷️ 自动发现了 9 个路由标签
2025-09-17 14:50:36,018 - main - INFO - certificate路由加载成功
2025-09-17 14:50:36,023 - main - INFO - fee路由加载成功
2025-09-17 14:50:36,026 - main - INFO - file路由加载成功
2025-09-17 14:50:36,032 - main - INFO - identity路由加载成功
2025-09-17 14:50:36,047 - main - INFO - intention路由加载成功
2025-09-17 14:50:36,080 - main - INFO - platform路由加载成功
2025-09-17 14:50:36,109 - main - INFO - saas路由加载成功
2025-09-17 14:50:36,144 - main - INFO - signing路由加载成功
2025-09-17 14:50:36,151 - main - INFO - wiki路由加载成功
2025-09-17 14:50:36,156 - main - INFO - 🚀 设置MCP服务...
2025-09-17 14:50:36,156 - main - INFO - 🏷️ 使用自动发现的标签: ['SaaS域', '费用域', '实名域', '文件域', '签署域', 'wiki域', '意愿域', '平台功能', '证书域']
2025-09-17 14:50:36,295 - fastapi_mcp.server - INFO - No auth config provided, skipping auth setup
2025-09-17 14:50:36,296 - fastapi_mcp.server - INFO - MCP SSE server listening at /mcp
2025-09-17 14:50:36,297 - main - INFO - ✅ MCP服务设置完成 - 使用官方FastApiMCP框架
2025-09-17 14:50:36,297 - main - INFO - 🔌 MCP端点: http://localhost:8000/mcp
2025-09-17 14:50:36,299 - main - INFO - 🚀 应用启动中...
2025-09-17 14:50:36,300 - app.core.connection_keeper - INFO - 连接保持器初始化完成，存储文件: data\mcp_connections.json
2025-09-17 14:50:36,300 - app.core.connection_keeper - INFO - 从文件恢复了 0 个连接
2025-09-17 14:50:36,301 - app.core.connection_keeper - INFO - 连接保持器后台任务已启动
2025-09-17 14:50:36,301 - main - INFO - ✅ 连接保持器已启动，支持连接不断和服务重启恢复
2025-09-17 14:50:36,301 - main - INFO - 🔥 开始预热 Swagger UI 文档缓存...
2025-09-17 14:50:36,301 - main - INFO - ✅ Swagger UI 文档缓存预热完成，耗时 0.00秒
2025-09-17 14:50:36,330 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:50:36,331 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "4438869840", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:50:36,331 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:50:38,542 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 200 OK"
2025-09-17 14:50:38,546 - mcpService.common.http_client - INFO - HTTP响应: 200
2025-09-17 14:50:38,546 - mcpService.common.http_client - INFO - 响应数据: {"code": 0, "message": "成功", "data": {"fileId": "3797bfab2e4b4fc8979b634e0f65e407", "uploadUrl": "https://esignoss.esign.cn/1111564182/562a84b0-ff27-4047-874c-1c25bf2575c4/3%E9%A1%B5.pdf?Expires=1758095438&OSSAccessKeyId=STS.NZmhUr1jytt95gxNamMZshRkY&Signature=GXAqd3KT0mOj100c049ElaNXVkI%3D&callback-var=eyJ4OmZpbGVfa2V5IjoiJDNhOGZmMDYyLTdmYTktNGUyYS04YjMwLWRhNDdmODUzZjE2YiQ2OTcxODc4MjQifQ%3D%3D%0A&callback=eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9zbWx0YXBpLnRzaWduLmNuL2FueWRvb3IvZmlsZS1zeXN0ZW0vY2FsbGJhY2svYWxpb3NzIiwiY2FsbGJhY2tCb2R5IjogIntcIm1pbWVUeXBlXCI6JHttaW1lVHlwZX0sXCJzaXplXCI6ICR7c2l6ZX0sXCJidWNrZXRcIjogJHtidWNrZXR9LFwib2JqZWN0XCI6ICR7b2JqZWN0fSxcImV0YWdcIjogJHtldGFnfSxcImZpbGVfa2V5XCI6JHt4OmZpbGVfa2V5fX0iLCJjYWxsYmFja0JvZHlUeXBlIjogImFwcGxpY2F0aW9uL2pzb24ifQ%3D%3D%0A&security-token=CAISxAJ1q6Ft5B2yfSjIr5nYI%2B%2FG3LVYw7bSN0HJqmE4QdVfh5fAuzz2IHtKdXRvBu8Xs%2F4wnmxX7f4YlqB6T55OSAmcNZEod1zfL%2F76MeT7oMWQweEurv%2FMQBqyaXPS2MvVfJ%2BOLrf0ceusbFbpjzJ6xaCAGxypQ12iN%2B%2Fm6%2FNgdc9FHHPPD1x8CcxROxFppeIDKHLVLozNCBPxhXfKB0ca0WgVy0EHsPnvm5DNs0uH1AKjkbRM9r6ceMb0M5NeW75kSMqw0eBMca7M7TVd8RAi9t0t1%2FIVpGiY4YDAWQYLv0rda7DOltFiMkpla7MmXqlft%2BhzcgeQY0pc%2FW6e6mGuXYk9O0y3LOgrUFvAtW1omPyN1KcpSpXcU%2B3%2Bp1TPyXcAS1g12v00dZTvxLH%2Bh%2BJCYs8X58VMgjO1iX9IIPPtZRAi98Qdpz0agAGSdfeawn%2F3WJnsawWBDkNu%2FURi%2FLtvD8dl3HP7KQSvorIwAA8aP3ixVHBv75HAtcN3fXschOPDxJvIJrukOgHxT07Y8iKbSLOw96Lkt8cgySyFx4zZBdmZsYRD1xvQtMIqa%2FNXAz8hnU2OT2JSnBGgxG0Ty6UhqMQeIw6lQ%2FfVxSAA"}}
2025-09-17 14:50:41,959 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'success': True, 'message': '成功', 'data': {'fileId': '3797bfab2e4b4fc8979b634e0f65e407', 'fileMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'appId': '4438869840', 'fileName': '3页.pdf', 'uploadStatus': 'success'}}
2025-09-17 14:50:49,429 - mcpService.common.http_client - INFO - HTTP请求: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl
2025-09-17 14:50:49,430 - mcpService.common.http_client - INFO - 请求头: {"Content-Type": "application/json", "X-Tsign-Open-Auth-Mode": "simple", "X-Tsign-Service-Group": "DEFAULT", "X-Tsign-Open-App-Id": "11111111", "X-Tsign-Open-Tenant-Id": "b0f99abbc3cd4d63a5a2a84c452e52d6"}
2025-09-17 14:50:49,430 - mcpService.common.http_client - INFO - 请求数据: {"contentMd5": "iAu2sVmSfF7LkbuV/7EDjw==", "contentType": "application/octet-stream;charset=UTF-8", "convert2Pdf": false, "fileName": "3页.pdf", "fileSize": 2542635}
2025-09-17 14:50:52,616 - httpx - INFO - HTTP Request: POST http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl "HTTP/1.1 401 Unauthorized"
2025-09-17 14:50:52,619 - mcpService.common.http_client - INFO - HTTP响应: 401
2025-09-17 14:50:52,620 - mcpService.common.http_client - INFO - 响应数据: {"success": false, "code": 401, "message": "无效的应用"}
2025-09-17 14:50:53,892 - app.mcpController.domains.file_controller - INFO - 获取上传地址完成: {'success': False, 'code': 401, 'message': '无效的应用', 'request_details': {'url': 'http://in-sml-ws-openapi.tsign.cn/v1/files/getUploadUrl', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Service-Group': 'DEFAULT', 'X-Tsign-Open-App-Id': '11111111', 'X-Tsign-Open-Tenant-Id': 'b0f99abbc3cd4d63a5a2a84c452e52d6'}, 'data': {'contentMd5': 'iAu2sVmSfF7LkbuV/7EDjw==', 'contentType': 'application/octet-stream;charset=UTF-8', 'convert2Pdf': False, 'fileName': '3页.pdf', 'fileSize': 2542635}, 'params': None, 'environment': 'test', 'domain': 'file'}, 'response_details': {'status_code': 401, 'headers': {'connection': 'close', 'content-length': '56', 'date': 'Wed, 17 Sep 2025 06:50:52 GMT', 'server': 'openresty', 'x-tsign-elapse-time': '0', 'x-tsign-trace-id': ''}, 'response_time': 'N/A'}}
2025-09-17 16:02:48,103 - main - INFO - 🛑 应用关闭中...
2025-09-17 16:02:48,105 - main - INFO - ✅ 连接状态已保存
2025-09-17 16:02:48,105 - main - INFO - ✅ 应用已关闭
