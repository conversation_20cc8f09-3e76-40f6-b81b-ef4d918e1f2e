#!/usr/bin/env python3
"""
测试启动时只调用一次自动发现
"""
import sys
import os
import logging
from io import StringIO

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_startup_once():
    print("=" * 60)
    print("测试启动时自动发现调用次数")
    print("=" * 60)
    
    # 捕获日志输出
    log_capture = StringIO()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.INFO)
    
    # 获取自动发现的logger
    auto_discovery_logger = logging.getLogger('app.core.auto_discovery')
    auto_discovery_logger.addHandler(handler)
    auto_discovery_logger.setLevel(logging.INFO)
    
    try:
        # 导入并创建应用（模拟启动过程）
        from main import create_app
        app = create_app()
        
        # 获取日志内容
        log_content = log_capture.getvalue()
        
        # 统计"开始自动发现"的出现次数
        discovery_start_count = log_content.count("开始自动发现")
        
        print(f"📊 统计结果:")
        print(f"   '开始自动发现' 出现次数: {discovery_start_count}")
        
        if discovery_start_count == 1:
            print("✅ 测试通过：启动时只调用一次自动发现")
            return True
        else:
            print(f"❌ 测试失败：预期1次，实际{discovery_start_count}次")
            print("\n📝 日志内容:")
            print(log_content)
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 清理handler
        auto_discovery_logger.removeHandler(handler)

if __name__ == "__main__":
    success = test_startup_once()
    if success:
        print("\n🎉 启动优化成功！")
    else:
        print("\n❌ 需要进一步优化")
        sys.exit(1)