#!/usr/bin/env python3
"""
完整系统测试
测试自动发现 + MCP装饰器 + 主应用
"""
import asyncio
import sys
import os
# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import create_app
from app.core.auto_discovery import get_modules_and_tags

async def test_system():
    print("=" * 60)
    print("完整系统测试")
    print("=" * 60)
    
    # 测试1: 自动发现功能
    print("\n1. 测试自动发现功能:")
    modules, tags = get_modules_and_tags()
    print(f"   发现模块: {len(modules)} 个")
    print(f"   发现标签: {len(tags)} 个")
    print(f"   标签列表: {tags}")
    
    # 测试2: 应用创建
    print("\n2. 测试应用创建:")
    try:
        app = create_app()
        print("   ✅ 应用创建成功")
        
        # 检查路由
        route_count = len(app.routes)
        print(f"   路由数量: {route_count}")
        
        # 检查MCP相关路由
        mcp_routes = [route for route in app.routes if '/mcp' in str(getattr(route, 'path', ''))]
        print(f"   MCP路由: {len(mcp_routes)} 个")
        
    except Exception as e:
        print(f"   ❌ 应用创建失败: {e}")
        return False
    
    # 测试3: 装饰器功能
    print("\n3. 测试装饰器功能:")
    try:
        from app.core.mcp_decorator import mcp_endpoint
        from fastapi import Query
        
        @mcp_endpoint
        async def test_endpoint(
            env: str = Query(..., description="环境类型，如'测试环境'")
        ):
            return {"status": "success", "env": env}
        
        # 测试参数错误
        result1 = await test_endpoint()
        if result1.get("status") == "parameter_error":
            print("   ✅ 参数校验正常")
        else:
            print("   ❌ 参数校验异常")
            
        # 测试正确参数
        result2 = await test_endpoint(env="测试环境")
        if result2.get("status") == "success":
            print("   ✅ 正常执行成功")
        else:
            print("   ❌ 正常执行失败")
            
    except Exception as e:
        print(f"   ❌ 装饰器测试失败: {e}")
        return False
    
    print("\n✅ 所有测试通过！系统运行正常")
    return True

if __name__ == "__main__":
    success = asyncio.run(test_system())
    if success:
        print("\n🎉 系统测试完成，可以启动服务了！")
        print("运行命令: python main.py")
    else:
        print("\n❌ 系统测试失败，请检查错误信息")
        sys.exit(1)