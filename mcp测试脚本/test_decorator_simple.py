#!/usr/bin/env python3
"""
简单测试MCP装饰器
"""
import asyncio
from app.core.mcp_decorator import mcp_endpoint
from fastapi import Query

@mcp_endpoint
async def test_endpoint(
    env: str = Query(..., description="环境类型，如'测试环境'"),
    flow_id: str = Query(..., description="流程ID，如'fc7095df4d4d4977a006342c1d629aba'")
):
    return {"status": "success", "env": env, "flow_id": flow_id}

async def main():
    print("测试1: 缺少参数")
    result1 = await test_endpoint()
    print(f"结果: {result1.get('status', 'unknown')}")
    
    print("\n测试2: 正确参数")
    result2 = await test_endpoint(env="测试环境", flow_id="fc7095df4d4d4977a006342c1d629aba")
    print(f"结果: {result2.get('status', 'unknown')}")

if __name__ == "__main__":
    asyncio.run(main())