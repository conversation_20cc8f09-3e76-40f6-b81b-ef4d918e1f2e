#!/usr/bin/env python3
"""
测试自动发现的__init__.py
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_auto_init():
    print("=" * 60)
    print("测试自动发现的__init__.py")
    print("=" * 60)
    
    try:
        # 测试导入
        from app.mcpController.domains import __all__
        print(f"✅ 成功导入 __all__: {len(__all__)} 个路由器")
        
        for router_name in __all__:
            print(f"  - {router_name}")
        
        # 测试动态导入
        import app.mcpController.domains as domains
        
        print(f"\n✅ 可用的路由器属性:")
        for attr_name in dir(domains):
            if attr_name.endswith('_router') and not attr_name.startswith('_'):
                attr_value = getattr(domains, attr_name)
                print(f"  - {attr_name}: {type(attr_value)}")
        
        # 测试具体路由器
        if hasattr(domains, 'certificate_router'):
            cert_router = domains.certificate_router
            print(f"\n✅ certificate_router 测试:")
            print(f"  类型: {type(cert_router)}")
            print(f"  标签: {getattr(cert_router, 'tags', 'N/A')}")
            print(f"  前缀: {getattr(cert_router, 'prefix', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_init()
    if success:
        print("\n🎉 自动发现的__init__.py测试通过！")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)