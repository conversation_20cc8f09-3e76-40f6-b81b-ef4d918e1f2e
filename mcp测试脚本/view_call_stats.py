#!/usr/bin/env python3
"""
查看接口调用统计脚本
用于查看所有接口的调用次数统计
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp测试脚本.call_stats import (
    get_all_call_stats,
    get_call_stats_by_prefix,
    get_top_calls,
    format_stats_report,
    reset_all_stats
)

def main():
    """主函数"""
    print("接口调用统计查看工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看所有接口调用统计")
        print("2. 按前缀查看接口调用统计")
        print("3. 查看调用次数最多的接口")
        print("4. 查看格式化统计报告")
        print("5. 重置所有调用统计")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-5): ").strip()
        
        if choice == "1":
            stats = get_all_call_stats()
            if stats:
                print("\n所有接口调用统计:")
                print("-" * 30)
                for interface, count in sorted(stats.items(), key=lambda x: x[1], reverse=True):
                    print(f"{interface}: {count}")
            else:
                print("\n暂无调用统计数据")
                
        elif choice == "2":
            prefix = input("请输入前缀 (如 certificate:, saas:, identity:): ").strip()
            if prefix:
                stats = get_call_stats_by_prefix(prefix)
                if stats:
                    print(f"\n前缀为 '{prefix}' 的接口调用统计:")
                    print("-" * 30)
                    for interface, count in sorted(stats.items(), key=lambda x: x[1], reverse=True):
                        print(f"{interface}: {count}")
                else:
                    print(f"\n未找到前缀为 '{prefix}' 的接口调用统计")
            else:
                print("前缀不能为空")
                
        elif choice == "3":
            try:
                limit = input("请输入要显示的接口数量 (默认10): ").strip()
                limit = int(limit) if limit else 10
                top_calls = get_top_calls(limit)
                if top_calls:
                    print(f"\n调用次数最多的前 {len(top_calls)} 个接口:")
                    print("-" * 40)
                    for item in top_calls:
                        print(f"{item['interface']}: {item['call_count']}")
                else:
                    print("\n暂无调用统计数据")
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == "4":
            report = format_stats_report()
            print("\n格式化统计报告:")
            print(report)
            
        elif choice == "5":
            confirm = input("确定要重置所有调用统计吗？此操作不可恢复！(y/N): ").strip().lower()
            if confirm == "y":
                reset_all_stats()
                print("已重置所有调用统计")
            else:
                print("操作已取消")
                
        elif choice == "0":
            print("退出程序")
            break
            
        else:
            print("无效选项，请重新输入")

if __name__ == "__main__":
    main()