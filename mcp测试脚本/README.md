# MCP自测工具

用于测试和验证MCP服务接口的工具集。

## 目录结构

```
mcp自测文件/
├── api_test_runner.py     # API测试核心逻辑
├── run_tests.py          # 运行测试（服务需预先启动）
├── run_full_test.py      # 完整测试（自动启动和停止服务）
├── call_stats.py         # 接口调用统计查看模块
├── view_call_stats.py    # 查看接口调用统计脚本
├── logs/                 # 测试日志目录
└── README.md             # 本说明文件
```

## 使用方法

### 1. API接口测试

#### 测试已运行的服务

如果服务已经在运行（例如在 `http://localhost:8000`），可以使用以下命令运行测试：

```bash
python run_tests.py
```

#### 完整测试流程（推荐）

此方法会自动启动服务，运行测试，然后停止服务：

```bash
python run_full_test.py
```

### 2. 接口调用统计查看

#### 命令行交互式查看

```bash
python view_call_stats.py
```

#### 编程方式查看

在Python代码中直接使用：

```python
from mcp测试脚本.call_stats import get_all_call_stats, get_top_calls

# 获取所有接口调用统计
all_stats = get_all_call_stats()
print(all_stats)

# 获取调用次数最多的前5个接口
top_calls = get_top_calls(5)
print(top_calls)
```

## 功能说明

### API测试工具

测试脚本会验证以下接口：

1. **健康检查接口**
   - `GET /health_check`

2. **证书域接口**
   - `GET /certificate/get_test_account`

3. **签署域接口**
   - `POST /signing/one_click_sign`

4. **SaaS域接口**
   - `POST /saas/register_test_person_account`

5. **费用域接口**
   - `POST /fee/add_app_quota`

6. **实名域接口**
   - `POST /identity/create_verification`

7. **意愿域接口**
   - `POST /intention/create_verification`

8. **平台功能接口**
   - `POST /platform/get_testcase_generation_prompt`

### 接口调用统计工具

提供以下功能：

1. **查看所有接口调用统计**
2. **按前缀查看接口调用统计**
3. **查看调用次数最多的接口**
4. **查看格式化统计报告**
5. **重置所有调用统计**

## 测试报告

测试完成后会生成两份报告：

1. **控制台报告** - 显示测试摘要和详细结果
2. **JSON文件报告** - 包含完整测试数据，文件名格式为 `test_report_YYYYMMDD_HHMMSS.json`

## 报告内容

报告包含以下信息：

- 测试总数量
- 成功/失败数量
- 成功率百分比
- 平均响应时间
- 每个接口的详细测试结果（包括响应时间、状态码等）

## 注意事项

1. 确保所有依赖包已安装（httpx等）
2. 如果使用方法二，确保没有其他进程占用8000端口
3. 部分接口可能因为业务逻辑返回500错误，但在测试中仍被视为"可达"
4. 测试使用模拟数据，不会对真实系统产生影响