#!/usr/bin/env python3
"""
测试运行脚本 - 假设服务已经运行，仅执行API测试
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp测试脚本.api_test_runner import APITestRunner

async def main():
    """主函数"""
    print("开始执行API测试...")
    
    # 创建测试运行器，假设服务在本地8000端口运行
    runner = APITestRunner("http://localhost:8000")
    
    try:
        # 运行所有测试
        report = await runner.run_all_tests()
        
        # 打印报告
        runner.print_report(report)
        
        # 保存报告到文件
        import json
        from datetime import datetime
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n测试报告已保存到: {report_file}")
        
        # 如果有失败的测试，返回非零退出码
        if report["test_summary"]["failed_tests"] > 0:
            sys.exit(1)
        else:
            print("所有测试通过!")
            
    except Exception as e:
        print(f"测试执行过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())