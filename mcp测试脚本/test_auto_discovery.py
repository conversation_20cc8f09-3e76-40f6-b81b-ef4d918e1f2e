#!/usr/bin/env python3
"""
测试自动发现功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.auto_discovery import auto_discover_all, get_modules_and_tags

if __name__ == "__main__":
    print("=" * 60)
    print("测试自动发现功能")
    print("=" * 60)
    
    modules, tags = get_modules_and_tags()
    
    print(f"\n发现的模块 ({len(modules)}):")
    for module in modules:
        print(f"  - {module}")
    
    print(f"\n发现的标签 ({len(tags)}):")
    for tag in tags:
        print(f"  - {tag}")