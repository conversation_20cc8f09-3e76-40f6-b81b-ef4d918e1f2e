#!/usr/bin/env python3
"""
调试装饰器
"""
import inspect
from fastapi import Query
from app.core.mcp_decorator import extract_param_schema, validate_params_smart

def test_endpoint(
    env: str = Query(..., description="环境类型，如'测试环境'"),
    flow_id: str = Query(..., description="流程ID，如'fc7095df4d4d4977a006342c1d629aba'")
):
    return {"status": "success"}

# 测试参数解析
sig = inspect.signature(test_endpoint)
print("函数签名:", sig)

for name, param in sig.parameters.items():
    print(f"参数 {name}:")
    print(f"  默认值: {param.default}")
    print(f"  默认值类型: {type(param.default)}")
    if hasattr(param.default, 'default'):
        print(f"  Query.default: {param.default.default}")
    if hasattr(param.default, 'description'):
        print(f"  Query.description: {param.default.description}")

# 测试schema提取
schema = extract_param_schema(sig)
print("\n提取的schema:")
print(f"必填参数: {schema['required']}")
print(f"可选参数: {schema['optional']}")

# 测试参数校验
test_params = {}  # 空参数
result = validate_params_smart(test_params, schema)
print(f"\n校验结果:")
print(f"是否有效: {result.is_valid}")
print(f"错误: {result.errors}")
print(f"建议: {result.suggestions}")