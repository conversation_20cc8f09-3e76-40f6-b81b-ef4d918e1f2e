#!/usr/bin/env python3
"""
完整测试脚本 - 启动服务并运行所有API测试
"""
import asyncio
import subprocess
import time
import sys
import os
import signal
from api_test_runner import APITestRunner

class FullTestRunner:
    def __init__(self):
        self.server_process = None
        self.server_port = 8000
        self.server_host = "localhost"
        
    def start_server(self):
        """启动服务"""
        print("正在启动服务...")
        try:
            # 使用 uvicorn 启动服务
            self.server_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "main:app", 
                "--host", self.server_host,
                "--port", str(self.server_port),
                "--log-level", "info"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print(f"服务启动中... PID: {self.server_process.pid}")
            return True
        except Exception as e:
            print(f"启动服务失败: {str(e)}")
            return False
    
    def wait_for_server(self, timeout=60):
        """等待服务启动完成"""
        print("等待服务启动...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                import httpx
                response = httpx.get(f"http://{self.server_host}:{self.server_port}/health_check", timeout=5)
                if response.status_code == 200:
                    print("服务启动成功!")
                    return True
            except Exception:
                pass
            
            time.sleep(2)
            print(".", end="", flush=True)
        
        print("\n服务启动超时!")
        return False
    
    def stop_server(self):
        """停止服务"""
        if self.server_process:
            print("正在停止服务...")
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("服务已停止")
            except subprocess.TimeoutExpired:
                print("服务无法正常停止，强制杀死进程...")
                self.server_process.kill()
                self.server_process.wait()
            except Exception as e:
                print(f"停止服务时发生错误: {str(e)}")
    
    async def run_tests(self):
        """运行API测试"""
        print("开始运行API测试...")
        runner = APITestRunner(f"http://{self.server_host}:{self.server_port}")
        report = await runner.run_all_tests()
        runner.print_report(report)
        return report

async def main():
    """主函数"""
    print("开始执行完整测试流程...")
    
    # 创建测试运行器
    test_runner = FullTestRunner()
    
    try:
        # 1. 启动服务
        if not test_runner.start_server():
            print("无法启动服务，测试终止")
            return
        
        # 2. 等待服务启动
        if not test_runner.wait_for_server():
            print("服务启动失败，测试终止")
            test_runner.stop_server()
            return
        
        # 3. 等待几秒钟确保服务完全就绪
        print("\n等待服务完全就绪...")
        time.sleep(3)
        
        # 4. 运行测试
        report = await test_runner.run_tests()
        
        # 5. 保存报告
        import json
        from datetime import datetime
        report_file = f"full_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n完整测试报告已保存到: {report_file}")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    finally:
        # 6. 停止服务
        test_runner.stop_server()
        print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())