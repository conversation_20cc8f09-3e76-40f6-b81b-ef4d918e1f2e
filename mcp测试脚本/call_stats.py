#!/usr/bin/env python3
"""
接口调用统计查看模块
用于查看所有接口的调用次数统计
"""
import logging
import redis
from typing import Dict, List, Any, Optional
import os
import glob
import re

logger = logging.getLogger(__name__)

class CallStatsViewer:
    """接口调用统计查看器"""
    
    def __init__(self, domains_path: str = None):
        self.redis_client = self._connect_redis()
        # 设置domains文件夹路径
        self.domains_path = domains_path or "mcpService/domains"
        # 动态扫描接口key列表
        self.interface_keys = self._scan_interface_keys()
    
    def _connect_redis(self):
        """连接Redis"""
        try:
            # 检查环境变量

            # 使用您提供的测试环境配置
            redis_host = "r-bp18f2292281c704.redis.rds.aliyuncs.com"
            redis_port =  6379
            redis_password = "secret#123456#"

            
            redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                decode_responses=False,  # 关闭自动解码，手动处理
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # 测试连接
            redis_client.ping()
            return redis_client
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return None
    

    
    def _safe_decode(self, data):
        """安全解码字节数据"""
        if isinstance(data, str):
            return data
        elif isinstance(data, bytes):
            try:
                return data.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return data.decode('gbk')
                except UnicodeDecodeError:
                    return data.decode('latin1', errors='ignore')
        else:
            return str(data)
    
    def _scan_interface_keys(self) -> List[str]:
        """
        动态扫描domains文件夹中的接口key
        
        Returns:
            List[str]: 扫描到的接口key列表
        """
        interface_keys = []
        
        try:
            print(f"🔍 开始扫描domains文件夹: {self.domains_path}")
            
            # 检查文件夹是否存在
            if not os.path.exists(self.domains_path):
                print(f"⚠️  domains文件夹不存在: {self.domains_path}")
                return self._get_default_keys()
            
            # 扫描所有Python文件
            python_files = glob.glob(os.path.join(self.domains_path, "*.py"))
            print(f"📁 找到 {len(python_files)} 个Python文件")
            
            for file_path in python_files:
                if file_path.endswith("__init__.py"):
                    continue
                    
                print(f"📄 正在扫描文件: {os.path.basename(file_path)}")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 从文件名推断业务域前缀
                    filename = os.path.basename(file_path)
                    domain_prefix = filename.replace('_controller.py', '').replace('_service.py', '')
                    
                    # 扫描函数定义，提取接口操作
                    function_pattern = r'def\s+(\w+)\s*\('
                    functions = re.findall(function_pattern, content)
                    
                    # 过滤出业务接口函数（排除私有方法和特殊方法）
                    business_functions = [
                        func for func in functions 
                        if not func.startswith('_') and func not in ['get', 'post', 'put', 'delete']
                    ]
                    
                    print(f"  📋 找到 {len(business_functions)} 个业务函数")
                    
                    # 生成接口key
                    for func in business_functions:
                        # 将函数名转换为接口操作名
                        operation = self._convert_function_to_operation(func)
                        if operation:
                            interface_key = f"{domain_prefix}:{operation}"
                            interface_keys.append(interface_key)
                            print(f"  ✅ 生成接口key: {interface_key}")
                
                except Exception as e:
                    print(f"⚠️  扫描文件 {file_path} 失败: {e}")
                    continue
            
            # 如果没有扫描到任何key，使用默认key
            if not interface_keys:
                print("⚠️  未扫描到任何接口key，使用默认配置")
                interface_keys = self._get_default_keys()
            
            print(f"🎉 扫描完成，总共找到 {len(interface_keys)} 个接口key")
            return interface_keys
            
        except Exception as e:
            print(f"❌ 扫描domains文件夹失败: {e}")
            return self._get_default_keys()
    
    def _convert_function_to_operation(self, func_name: str) -> str:
        """
        将函数名转换为接口操作名
        
        Args:
            func_name (str): 函数名
            
        Returns:
            str: 操作名
        """
        # 常见的函数名到操作名的映射
        operation_mapping = {
            'create': 'create',
            'add': 'create', 
            'new': 'create',
            'query': 'query',
            'get': 'query',
            'find': 'query',
            'search': 'query',
            'list': 'list',
            'update': 'update',
            'modify': 'update',
            'edit': 'update',
            'delete': 'delete',
            'remove': 'delete',
            'cancel': 'cancel',
            'revoke': 'revoke',
            'download': 'download',
            'upload': 'upload',
            'login': 'login',
            'logout': 'logout',
            'register': 'register',
            'verify': 'verify',
            'confirm': 'confirm',
            'start': 'start',
            'stop': 'stop',
            'pause': 'pause',
            'resume': 'resume'
        }
        
        func_lower = func_name.lower()
        
        # 直接匹配
        for key, operation in operation_mapping.items():
            if key in func_lower:
                return operation
        
        # 如果没有匹配到，返回原函数名（转小写）
        return func_lower
    
    def _get_default_keys(self) -> List[str]:
        """
        获取默认的接口key列表（作为备用）
        
        Returns:
            List[str]: 默认接口key列表
        """
        return [
            "certificate:create",
            "certificate:query", 
            "certificate:download",
            "certificate:revoke",
            "signing:create",
            "signing:start",
            "signing:query",
            "signing:cancel",
            "saas:login",
            "saas:logout",
            "saas:register",
            "identity:create",
            "identity:verify",
            "identity:query",
            "intention:create",
            "intention:confirm",
            "intention:query",
            "platform:query",
            "platform:create"
        ]
    
    def get_interface_keys(self) -> List[str]:
        """
        获取接口统计key列表
        
        Returns:
            List[str]: 接口key列表
        """
        print(f"📋 当前监控的 {len(self.interface_keys)} 个接口key")
        for i, key in enumerate(self.interface_keys, 1):
            print(f"  {i}. {key}")
        return self.interface_keys
    
    def refresh_interface_keys(self):
        """
        重新扫描并刷新接口key列表
        """
        print("🔄 重新扫描接口key...")
        old_count = len(self.interface_keys)
        self.interface_keys = self._scan_interface_keys()
        new_count = len(self.interface_keys)
        print(f"🎉 刷新完成: {old_count} -> {new_count} 个接口key")
    
    def add_interface_key(self, key: str):
        """
        添加新的接口统计key
        
        Args:
            key (str): 要添加的接口key
        """
        if key not in self.interface_keys:
            self.interface_keys.append(key)
            print(f"✅ 已添加新接口key: {key}")
        else:
            print(f"⚠️  接口key已存在: {key}")
    
    def get_single_stat(self, key: str) -> int:
        """
        获取单个接口的调用统计
        
        Args:
            key (str): 接口key
            
        Returns:
            int: 调用次数
        """
        if not self.redis_client:
            print("❌ Redis客户端未初始化")
            return 0
        
        try:
            print(f"🔍 正在获取key '{key}' 的值...")
            val = self.redis_client.get(key)
            if val:
                try:
                    decoded_val = self._safe_decode(val)
                    count = int(decoded_val)
                    print(f"✅ key '{key}' 的值为: {count}")
                    return count
                except ValueError:
                    print(f"⚠️  key '{key}' 的值无法转换为数字，返回0")
                    return 0
            else:
                print(f"⚪ key '{key}' 无值，返回0")
                return 0
        except Exception as e:
            print(f"❌ 获取key '{key}' 失败: {e}")
            return 0
    
    def get_all_call_stats(self) -> Dict[str, int]:
        """
        获取所有接口的调用统计
        
        Returns:
            Dict[str, int]: 以接口名为key，调用次数为value的字典
        """
        if not self.redis_client:
            print("❌ Redis客户端未初始化")
            return {}
        
        try:
            # 获取预定义的接口key列表
            interface_keys = self.get_interface_keys()
            
            print(f"📊 开始获取 {len(interface_keys)} 个接口的调用统计...")
            
            # 直接查询这些key的值
            call_stats = {}
            for i, key in enumerate(interface_keys, 1):
                print(f"🔍 ({i}/{len(interface_keys)}) 正在获取key '{key}' 的值...")
                val = self.redis_client.get(key)
                if val:
                    try:
                        decoded_val = self._safe_decode(val)
                        call_stats[key] = int(decoded_val)
                        print(f"✅ key '{key}' 的值为: {call_stats[key]}")
                    except ValueError:
                        call_stats[key] = 0
                        print(f"⚠️  key '{key}' 的值无法转换为数字，设为0")
                else:
                    call_stats[key] = 0
                    print(f"⚪ key '{key}' 无值，设为0")
            
            valid_count = len([k for k, v in call_stats.items() if v > 0])
            print(f"🎉 成功获取 {valid_count} 个有调用记录的接口统计")
            return call_stats
        except Exception as e:
            print(f"❌ 获取调用统计信息失败: {e}")
            return {}
    
    def get_call_stats_by_prefix(self, prefix: str) -> Dict[str, int]:
        """
        根据前缀获取接口的调用统计
        
        Args:
            prefix (str): key前缀，如 "certificate:", "saas:" 等
            
        Returns:
            Dict[str, int]: 以接口名为key，调用次数为value的字典
        """
        if not self.redis_client:
            print("❌ Redis客户端未初始化")
            return {}
        
        try:
            # 从预定义key列表中过滤出匹配前缀的key
            filtered_keys = [key for key in self.interface_keys if key.startswith(prefix)]
            
            print(f"🔍 根据前缀 '{prefix}' 过滤出 {len(filtered_keys)} 个key")
            if filtered_keys:
                print("📝 匹配的key列表:")
                for i, key in enumerate(filtered_keys, 1):
                    print(f"  {i}. {key}")
            
            # 直接查询这些key的值
            call_stats = {}
            for i, key in enumerate(filtered_keys, 1):
                print(f"🔍 ({i}/{len(filtered_keys)}) 正在获取key '{key}' 的值...")
                val = self.redis_client.get(key)
                if val:
                    try:
                        decoded_val = self._safe_decode(val)
                        call_stats[key] = int(decoded_val)
                        print(f"✅ key '{key}' 的值为: {call_stats[key]}")
                    except ValueError:
                        call_stats[key] = 0
                        print(f"⚠️  key '{key}' 的值无法转换为数字，设为0")
                else:
                    call_stats[key] = 0
                    print(f"⚪ key '{key}' 无值，设为0")
            
            valid_count = len([k for k, v in call_stats.items() if v > 0])
            print(f"🎉 成功获取前缀为 '{prefix}' 的 {valid_count} 个有调用记录的接口统计")
            return call_stats
        except Exception as e:
            print(f"❌ 根据前缀获取调用统计信息失败: {e}")
            return {}
    
    def get_top_calls(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取调用次数最多的接口
        
        Args:
            limit (int): 返回的接口数量限制，默认为10
            
        Returns:
            List[Dict[str, Any]]: 包含接口名和调用次数的列表，按调用次数降序排列
        """
        all_stats = self.get_all_call_stats()
        
        # 按调用次数排序并取前limit个
        sorted_stats = sorted(all_stats.items(), key=lambda x: x[1], reverse=True)
        
        logger.info(f"获取调用次数最多的前 {min(limit, len(sorted_stats))} 个接口")
        
        # 格式化结果
        result = []
        for i, (key, count) in enumerate(sorted_stats[:limit], 1):
            item = {
                "interface": key,
                "call_count": count
            }
            result.append(item)
            logger.debug(f"  {i}. {key}: {count}")
            
        logger.info(f"成功获取前 {len(result)} 个接口的调用统计")
        return result
    
    def format_stats_report(self) -> str:
        """
        格式化统计报告
        
        Returns:
            str: 格式化的统计报告
        """
        all_stats = self.get_all_call_stats()
        
        if not all_stats:
            return "暂无调用统计数据"
        
        # 按调用次数排序
        sorted_stats = sorted(all_stats.items(), key=lambda x: x[1], reverse=True)
        
        # 构建报告
        report_lines = ["=" * 50]
        report_lines.append("接口调用统计报告")
        report_lines.append("=" * 50)
        report_lines.append(f"{'接口名称':<30} {'调用次数':<10}")
        report_lines.append("-" * 50)
        
        total_calls = 0
        for key, count in sorted_stats:
            if count > 0:  # 只显示有调用记录的接口
                report_lines.append(f"{key:<30} {count:<10}")
            total_calls += count
            
        report_lines.append("-" * 50)
        report_lines.append(f"{'总计':<30} {total_calls:<10}")
        report_lines.append("=" * 50)
        
        return "\n".join(report_lines)
    
    def reset_all_stats(self):
        """
        重置所有调用统计（将所有统计key的值设为0）
        """
        if not self.redis_client:
            print("❌ Redis客户端未初始化")
            return
        
        try:
            # 重置预定义的接口key
            interface_keys = self.interface_keys
            
            print(f"🔄 开始重置 {len(interface_keys)} 个统计项...")
            
            for i, key in enumerate(interface_keys, 1):
                try:
                    self.redis_client.set(key, 0)
                    print(f"✅ ({i}/{len(interface_keys)}) 已重置key '{key}' 为 0")
                except Exception as e:
                    print(f"❌ 重置key {key} 失败: {e}")
            
            print(f"🎉 已重置 {len(interface_keys)} 个统计项")
        except Exception as e:
            print(f"❌ 重置调用统计信息失败: {e}")

# 便捷函数
def get_all_call_stats(domains_path: str = None) -> Dict[str, int]:
    """获取所有接口的调用统计"""
    viewer = CallStatsViewer(domains_path)
    return viewer.get_all_call_stats()

def get_call_stats_by_prefix(prefix: str, domains_path: str = None) -> Dict[str, int]:
    """根据前缀获取接口的调用统计"""
    viewer = CallStatsViewer(domains_path)
    return viewer.get_call_stats_by_prefix(prefix)

def get_top_calls(limit: int = 10, domains_path: str = None) -> List[Dict[str, Any]]:
    """获取调用次数最多的接口"""
    viewer = CallStatsViewer(domains_path)
    return viewer.get_top_calls(limit)

def format_stats_report(domains_path: str = None) -> str:
    """格式化统计报告"""
    viewer = CallStatsViewer(domains_path)
    return viewer.format_stats_report()

def reset_all_stats(domains_path: str = None):
    """重置所有调用统计"""
    viewer = CallStatsViewer(domains_path)
    viewer.reset_all_stats()

if __name__ == "__main__":
    # 配置日志输出到控制台
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 示例用法
    print("🚀 接口调用统计查看工具启动")
    print("=" * 60)
    
    try:
        # 可以指定domains文件夹路径，如果不指定则使用默认路径
        domains_path = "mcpService/domains"  # 根据实际路径调整
        
        print("📊 开始生成统计报告...")
        report = format_stats_report(domains_path)
        print(report)
        
        # 演示其他功能
        print("\n" + "=" * 60)
        print("📋 演示其他功能:")
        
        # 查看证书相关接口统计
        cert_stats = get_call_stats_by_prefix("certificate", domains_path)
        print(f"\n证书相关接口统计: {cert_stats}")
        
        # 查看Top 5调用最多的接口
        top_calls = get_top_calls(5, domains_path)
        print(f"\nTop 5调用最多的接口: {top_calls}")
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        traceback.print_exc()