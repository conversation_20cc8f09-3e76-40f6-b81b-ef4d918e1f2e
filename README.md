# esign-qa-mcp-service

提供一个基于 FastAPI 的 MCP 协议服务，用于支持电子签名平台的 QA 自动化测试与接口模拟。

## 🚀 核心特性

- **智能参数校验**：参数错误时自动提供详细指导和修正建议
- **自动发现机制**：零配置自动发现控制器和路由标签
- **API调用统计**：实时统计所有端点调用次数到Redis
- **性能优化**：缓存机制避免重复自动发现，提升启动速度
- **统一异常处理**：友好的错误提示和参数引导
- 支持 FastAPI-MCP 扩展协议
- 异步 HTTP 请求处理
- 测试用例自动生成
- XMind格式转换
- 平台集成服务

## 目录结构

```
.
├── app/                      # 应用核心代码
│   ├── core/                 # 核心配置（自动发现、装饰器、统计）
│   ├── mcpController/        # 控制器层
│   └── testCasePromptAndFiles/ # 测试用例提示词文件
├── mcpService/               # 服务层
│   ├── common/               # 通用模块
│   ├── domains/              # 各业务域服务
│   └── platform/             # 平台功能服务
├── mcp测试脚本/              # 自动化测试工具
├── data/                     # 数据文件
├── logs/                     # 日志文件
├── assets/                   # 资源文件
├── Dockerfile                # Docker构建文件
├── requirements.txt          # 依赖包列表
└── main.py                   # 主程序入口
```

## 快速开始

### 环境要求

- Python 3.11
- pip

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动服务

#### 开发模式

```bash
uvicorn main:app --reload
```

或

```bash
python main.py
```

#### 生产模式

```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Docker部署

```bash
# 构建镜像
docker build -t esign-qa-mcp-service .

# 运行容器
docker run -d -p 8000:8000 esign-qa-mcp-service
```

## API文档

服务启动后，可通过以下地址访问API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- API调用数据统计: http://localhost:8000/mcp/call-stats

## MCP配置

### 环境MCP地址

```
http://qa-mcp-server-testvpc-svc1.local-test.svc.cluster.local:8000/mcp
```

**重要说明**：
- 此地址为Kubernetes集群内部直连地址，绕过了nginx代理
- 避免了nginx超时限制，确保长时间运行的MCP操作不会被中断
- 仅在集群内部网络环境中可访问

**技术原理**：
- 直连主机地址，未经过nginx处理
- 解决了nginx默认超时配置导致的连接中断问题
- 提供更稳定的MCP服务连接

**参考文档**：[MCP服务配置说明](http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221512509)

## 自动化测试

项目包含完整的自动化测试工具，位于 `tests/` 目录中。

### 运行测试

```bash
cd mcp测试脚本
python run_tests.py        # 测试已运行的服务
python run_full_test.py    # 完整测试（自动启停服务）
```

详细使用说明请查看 [tests/README.md](mcp测试脚本/README.md)

## 功能模块

### MCP协议支持

- 证书域 (Certificate) - 数字证书管理
- 签署域 (Signing) - 电子签名流程
- SaaS域 (SaaS) - 账号与组织管理
- 实名域 (Identity) - 身份认证
- 意愿域 (Intention) - 签署意愿验证
- 平台功能 (Platform) - 代码生成、测试用例、XMind转换
- 费用域 (Fee) - 企业配额充值
- Wiki域 (Wiki) - Wiki内容管理

## 📊 API调用统计

系统内置完整的API调用统计功能：

- **实时统计**: 所有API端点调用次数实时记录到Redis
- **统计覆盖**: 覆盖全部41个API端点
- **数据持久化**: 统计数据存储在阿里云Redis中
- **便捷查询**: 支持单个方法或全量统计查询
- **数据重置**: 支持指定方法或全量重置统计

**HTTP接口调用**：
```bash
# 获取所有API调用统计
GET /mcp/call-stats

# 返回示例
{
  "status": "success",
  "data": {
    "create_certificate_endpoint": 15,
    "query_certificate_detail_endpoint": 8,
    ...
  },
  "total_calls": 156,
  "total_endpoints": 41
}
```

## ⚡ 性能优化

- **缓存机制**：自动发现结果缓存，避免重复扫描
- **启动优化**：减少50%的自动发现时间
- **内存效率**：避免重复的数据结构创建
- **日志清洁**：消除重复日志输出



## 🎯 智能参数校验系统

### MCP端点装饰器 `@mcp_endpoint`

集成参数校验、调用统计、异常处理的一体化装饰器：

```python
from app.core.mcp_decorator import mcp_endpoint

@router.post("/your_endpoint")
@mcp_endpoint  # 集成参数校验 + 调用统计 + 异常处理
async def your_endpoint_function(
    env: str = Query(..., description="环境类型，如'测试环境'"),
    flow_id: str = Query(..., description="流程ID，如'fc7095df4d4d4977a006342c1d629aba'")
):
    # 你的业务逻辑
    pass
```

**装饰器功能**：
- 🔍 **智能参数校验**：自动校验必填参数和格式
- 📊 **调用统计**：自动记录API调用次数
- 🛡️ **异常处理**：统一的错误处理和友好提示
- 💡 **参数引导**：错误时提供详细的修正建议

**参数错误时的友好提示**：
```json
{
  "status": "parameter_error",
  "message": "参数校验失败，请检查以下问题：",
  "errors": ["缺少必填参数: env"],
  "suggestions": ["请提供 env 参数 (环境类型，如'测试环境')，例如: 测试环境"],
  "required_params": {...},
  "optional_params": {...}
}
```

## 🔍 自动发现系统

**零配置开发体验**：
1. 在 `mcpService/domains/` 中创建服务模块
2. 在 `app/mcpController/domains/` 中创建控制器模块
3. 为所有端点方法添加 `@mcp_endpoint` 装饰器
4. **就这样！** 系统会自动：
   - 🔍 发现新控制器模块
   - 🏷️ 提取路由标签
   - 📊 添加到API统计
   - 🚀 配置MCP服务

**不再需要手动**：
- ❌ 修改 `main.py` 的模块列表
- ❌ 修改 `__init__.py` 的导入语句
- ❌ 维护标签列表

**自动发现结果**：
```
发现的模块 (9个): 证书域、签署域、SaaS域、实名域、意愿域、平台功能、费用域、wiki域、文件域
发现的标签 (9个): 自动提取并配置
```

## 配置

通过环境变量或 `.env` 文件配置：

```env
# 基础配置
base_url=http://in-test-openapi.tsign.cn
app_id=3438757422
is_mock=false

# 请求头配置
X-Tsign-Open-Tenant-Id=b0f99abbc3cd4d63a5a2a84c452e52d6

# Redis配置（API统计）
REDIS_HOST=r-bp18f2292281c704.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PASSWORD=secret#123456#

# Wiki配置
WIKI_BASE_URL=http://wiki.timevale.cn:8081
WIKI_LOGIN_URL=https://zerotrust.esign.cn/submit
WIKI_USERNAME=afei
WIKI_PASSWORD=feiA12345@
```

## 技术栈

- Python 3.11
- FastAPI 0.116.1
- Uvicorn 0.23.0
- fastapi-mcp 0.4.0
- httpx 0.24.0
- redis 4.5.0
- Jinja2 3.1.0
- structlog 23.0.0

## 日志

日志文件保存在 `logs/` 目录中，按日期滚动。

## 开发指南

### 代码规范

- 使用 black、isort、flake8 进行代码质量控制
- 遵循项目现有的代码风格

### 添加新功能

创建新控制器只需：
1. 在 `app/mcpController/domains/` 中创建 `*_controller.py` 文件
2. 使用 `@mcp_endpoint` 装饰器标记端点
3. 系统自动发现和配置 ✨

### 控制器标准结构

```python
from fastapi import APIRouter, Query
from app.core.mcp_decorator import mcp_endpoint

router = APIRouter(
    prefix="/domain",
    tags=["域名"],
    responses={404: {"description": "Not found"}}
)

@router.post("/endpoint")
@mcp_endpoint
async def endpoint_function(
    env: str = Query(..., description="环境类型，如'测试环境'"),
    param: str = Query(..., description="参数说明，如'示例值'")
):
    return {"status": "success"}
```

## 🎉 系统升级亮点

### 用户体验提升

**使用前 vs 使用后**：

**使用前**：
- 用户不知道需要哪些参数
- 参数错误时返回原始FastAPI错误
- 需要查看文档或代码才能知道参数格式
- 手动维护模块和标签列表

**使用后**：
- 参数错误时自动提供详细指导
- 清楚显示必填/可选参数
- 提供具体的参数示例和格式说明
- 自动发现和配置，无需手动维护

### 开发效率提升
- ✅ 新增控制器：0配置，自动发现
- ✅ 重命名控制器：0配置，自动适应  
- ✅ 删除控制器：0配置，自动清理
- ✅ 无需手动维护配置文件

### 系统稳定性
- ✅ 容错处理，发现失败时回退
- ✅ 详细日志，便于问题排查
- ✅ 向后兼容，不影响现有功能
- ✅ 统一的异常处理和错误格式

## 许可证

[待定]