#!/usr/bin/env python3
"""
主启动文件 - 使用 fastapi_mcp 自动暴露 MCP 工具
基于官方FastMCP框架，不做魔改，只做连接保持优化
"""
import asyncio
import json
import logging
# 配置日志
import os
import sys
import time
from contextlib import asynccontextmanager
from logging.handlers import TimedRotatingFileHandler

import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi_mcp import FastApiMCP

from app.core.config import *
from app.core.connection_keeper import get_connection_keeper

# 确保logs目录存在
os.makedirs("logs", exist_ok=True)

# 生成日志文件名（按日期）
log_filename = f"logs/service.log"

# 配置日志格式
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 配置按日期滚动的文件处理器
file_handler = TimedRotatingFileHandler(
    log_filename,
    when='midnight',  # 每天午夜滚动
    interval=1,  # 每天一个文件
    backupCount=30,  # 保留30天的日志
    encoding='utf-8'
)
file_handler.suffix = "%Y-%m-%d"  # 归档文件名格式
file_handler.setFormatter(logging.Formatter(log_format))

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format=log_format,
    encoding='utf-8',
    handlers=[
        # 文件处理器 - 按日期滚动
        file_handler,
        # 控制台处理器 - 同时输出到控制台
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 服务配置
SERVER_HOST = os.getenv("HOST", "0.0.0.0")
SERVER_PORT = int(os.getenv("PORT", "8000"))
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# 连接保持配置 - 针对SSE长连接优化
KEEP_ALIVE_TIMEOUT = int(os.getenv("KEEP_ALIVE_TIMEOUT", "604800"))  # 7天
GRACEFUL_SHUTDOWN_TIMEOUT = int(os.getenv("GRACEFUL_SHUTDOWN_TIMEOUT", "300"))  # 5分钟
MAX_CONCURRENCY = int(os.getenv("MAX_CONCURRENCY", "1000"))
MAX_REQUESTS = int(os.getenv("MAX_REQUESTS", "10000"))


def create_sse_headers() -> dict:
    """创建SSE专用响应头 - 确保连接不断"""
    return {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
        "X-Accel-Buffering": "no",  # 关键：禁用Nginx缓冲
        "Connection": "keep-alive",
        "Content-Type": "text/event-stream",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
        "Access-Control-Allow-Methods": "*"
    }


# Lifespan事件处理器 - 替代on_event
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动事件 - 初始化连接保持器
    logger.info("🚀 应用启动中...")
    connection_keeper = get_connection_keeper()
    await connection_keeper.start_background_tasks()
    logger.info("✅ 连接保持器已启动，支持连接不断和服务重启恢复")

    # 预热 Swagger UI 文档缓存
    await warmup_docs_cache(app)

    yield
    # 关闭事件 - 清理资源
    logger.info("🛑 应用关闭中...")
    # 保存连接状态到文件
    try:
        connection_keeper._save_connections()
        logger.info("✅ 连接状态已保存")
    except Exception as e:
        logger.error(f"❌ 保存连接状态失败: {e}")
    logger.info("✅ 应用已关闭")


# 全局缓存自动发现结果，避免重复调用
_cached_modules = None
_cached_tags = None

# Swagger UI 文档缓存
_docs_cache = None


async def warmup_docs_cache(app: FastAPI):
    """预热 Swagger UI 文档缓存"""
    global _docs_cache

    if _docs_cache is None:
        try:
            logger.info("🔥 开始预热 Swagger UI 文档缓存...")
            start_time = time.time()

            # 直接生成 Swagger UI HTML
            openapi_url = app.openapi_url or "/openapi.json"

            swagger_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{PROJECT_NAME} - API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.9.0/favicon-32x32.png" sizes="32x32" />
    <style>
        html {{
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }}
        *, *:before, *:after {{
            box-sizing: inherit;
        }}
        body {{
            margin:0;
            background: #fafafa;
        }}
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {{
            const ui = SwaggerUIBundle({{
                url: '{openapi_url}',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            }});
        }};
    </script>
</body>
</html>
            """

            _docs_cache = swagger_html

            elapsed = time.time() - start_time
            logger.info(f"✅ Swagger UI 文档缓存预热完成，耗时 {elapsed:.2f}秒")

        except Exception as e:
            logger.warning(f"⚠️ Swagger UI 文档缓存预热失败: {e}")
    else:
        logger.info("📋 Swagger UI 文档缓存已存在，跳过预热")


def get_cached_modules_and_tags():
    """获取缓存的模块和标签，避免重复自动发现"""
    global _cached_modules, _cached_tags

    if _cached_modules is None or _cached_tags is None:
        from app.core.auto_discovery import get_modules_and_tags
        _cached_modules, _cached_tags = get_modules_and_tags()
        logger.info(f"🔍 自动发现了 {len(_cached_modules)} 个控制器模块")
        logger.info(f"🏷️ 自动发现了 {len(_cached_tags)} 个路由标签")

    return _cached_modules, _cached_tags


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=PROJECT_NAME,
        description=MCP_DESCRIPTION,
        version=VERSION,
        docs_url=None,  # 禁用默认文档，使用自定义缓存版本
        redoc_url="/redoc",
        lifespan=lifespan  # 使用lifespan替代on_event
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 使用缓存的自动发现结果
    domain_modules, discovered_tags = get_cached_modules_and_tags()

    for module_path in domain_modules:
        try:
            module = __import__(module_path, fromlist=[''])
            if hasattr(module, 'router'):
                app.include_router(module.router)
                domain_name = module_path.split('.')[-1].replace('_controller', '')
                logger.info(f"{domain_name}路由加载成功")
        except Exception as e:
            logger.error(f"{module_path}路由加载失败: {str(e)}")

    # 自定义 Swagger UI 文档端点 - 带缓存优化
    @app.get("/docs", include_in_schema=False)
    async def custom_docs():
        """优化的 Swagger UI 文档 - 启动时预热，访问时直接返回缓存"""
        global _docs_cache

        if _docs_cache is None:
            # 理论上不应该到这里，因为启动时已经预热了
            logger.warning("⚠️ 文档缓存未找到，正在重新生成...")
            await warmup_docs_cache(app)

        logger.debug("📋 返回缓存的 Swagger UI 文档")
        return HTMLResponse(_docs_cache)

    # 添加健康检查端点
    @app.get("/health_check")
    async def health_check():
        return {
            "code": 0,
            "message": "ok",
            "data": {
                "server": "esign-qa-mcp-platform",
                "fastmcp_enabled": True,
                "connection_keeper_enabled": True,
                "keep_alive_timeout": KEEP_ALIVE_TIMEOUT,
                "version": VERSION,
                "docs_cached": _docs_cache is not None
            }
        }

    # 连接保持端点 - 基于FastMCP框架的连接不断功能
    @app.get("/mcp/connection-status")
    async def connection_status(request: Request):
        """获取连接状态 - 支持连接不断"""
        connection_keeper = get_connection_keeper()

        # 从请求头获取连接ID
        conn_id = request.headers.get("X-Connection-ID")

        # 如果有连接ID，更新活动时间
        if conn_id:
            await connection_keeper.update_connection_activity(conn_id)

        # 获取状态信息
        status = await connection_keeper.get_connection_status(conn_id)

        return {
            "success": True,
            "data": status,
            "message": "连接状态正常",
            "timestamp": int(time.time())
        }

    # 连接注册端点
    @app.post("/mcp/register-connection")
    async def register_connection(request: Request):
        """注册新连接 - 支持服务重启后恢复"""
        connection_keeper = get_connection_keeper()

        # 获取客户端信息
        client_info = {
            "user_agent": request.headers.get("user-agent", "unknown"),
            "client_ip": request.client.host if request.client else "unknown",
            "registered_at": time.time()
        }

        # 注册连接
        conn_id = await connection_keeper.register_connection(client_info=client_info)

        return {
            "success": True,
            "data": {
                "connection_id": conn_id,
                "status": "registered",
                "keep_alive_interval": 30,
                "auto_reconnect": True
            },
            "message": "连接注册成功"
        }

    # SSE心跳端点 - 确保MCP连接不断
    @app.get("/mcp/sse-heartbeat")
    async def sse_heartbeat(request: Request):
        """SSE心跳端点 - 基于FastMCP框架的连接保持"""
        connection_keeper = get_connection_keeper()

        # 获取或创建连接ID
        conn_id = request.headers.get("X-Connection-ID")
        if not conn_id:
            conn_id = await connection_keeper.register_connection()

        # 更新连接活动
        await connection_keeper.update_connection_activity(conn_id)

        # 获取连接状态
        status = await connection_keeper.get_connection_status(conn_id)

        # 添加SSE专用信息
        sse_data = {
            **status,
            "sse_enabled": True,
            "keep_alive": True,
            "auto_reconnect": True,
            "server": "esign-qa-mcp-platform",
            "mcp_compatible": True,
            "timestamp": time.time()
        }

        return Response(
            content=json.dumps(sse_data),
            media_type="application/json",
            headers=create_sse_headers()
        )

    # SSE流式心跳 - 持续保持MCP连接
    @app.get("/mcp/sse-stream")
    async def sse_stream(request: Request):
        """SSE流式心跳 - 确保MCP连接永不断开"""
        connection_keeper = get_connection_keeper()

        # 获取或创建连接ID
        conn_id = request.headers.get("X-Connection-ID")
        if not conn_id:
            conn_id = await connection_keeper.register_connection()

        async def heartbeat_generator():
            counter = 0
            try:
                while True:
                    counter += 1

                    # 更新连接活动
                    await connection_keeper.update_connection_activity(conn_id)

                    # 获取连接状态
                    status = await connection_keeper.get_connection_status(conn_id)

                    # 构建SSE数据
                    sse_data = {
                        **status,
                        "type": "heartbeat",
                        "counter": counter,
                        "sse_stream": True,
                        "mcp_ready": True,
                        "timestamp": time.time()
                    }

                    # SSE格式输出
                    yield f"data: {json.dumps(sse_data)}\n\n"

                    # 30秒心跳间隔
                    await asyncio.sleep(30)

            except asyncio.CancelledError:
                logger.info(f"SSE流式连接关闭: {conn_id}")
                await connection_keeper.remove_connection(conn_id)
            except Exception as e:
                logger.error(f"SSE流式连接错误: {str(e)}")
                await connection_keeper.remove_connection(conn_id)

        return StreamingResponse(
            heartbeat_generator(),
            media_type="text/event-stream",
            headers=create_sse_headers()
        )

    # API调用统计端点
    @app.get("/mcp/call-stats")
    async def get_call_stats():
        """获取API调用统计"""
        from app.core.call_stats import get_all_call_stats
        stats = get_all_call_stats()
        return {
            "success": True,
            "data": stats,
            "message": "获取调用统计成功",
            "timestamp": int(time.time())
        }

    @app.get("/mcp/call-stats/{method_name}")
    async def get_method_call_stats(method_name: str):
        """获取指定方法的调用统计"""
        from app.core.call_stats import get_method_call_count
        count = get_method_call_count(method_name)
        return {
            "success": True,
            "data": {
                "method_name": method_name,
                "call_count": count
            },
            "message": f"获取{method_name}调用统计成功",
            "timestamp": int(time.time())
        }

    @app.post("/mcp/call-stats/reset")
    async def reset_call_stats(method_name: str = None):
        """重置调用统计"""
        from app.core.call_stats import reset_call_stats
        reset_call_stats(method_name)
        message = f"重置{method_name}统计成功" if method_name else "重置所有统计成功"
        return {
            "success": True,
            "message": message,
            "timestamp": int(time.time())
        }

    # MCP连接状态检查 - 兼容FastMCP框架
    @app.get("/mcp/status")
    async def mcp_status():
        """MCP服务状态检查 - 确保服务可用"""
        connection_keeper = get_connection_keeper()
        status = await connection_keeper.get_connection_status()

        return Response(
            content=json.dumps({
                "mcp_service": "running",
                "fastmcp_enabled": True,
                "sse_support": True,
                "connection_keeper": status,
                "endpoints": {
                    "mcp": "/mcp",
                    "sse_heartbeat": "/mcp/sse-heartbeat",
                    "sse_stream": "/mcp/sse-stream",
                    "connection_status": "/mcp/connection-status"
                }
            }),
            media_type="application/json",
            headers=create_sse_headers()
        )

    # 设置 MCP 服务 - 使用官方FastApiMCP框架
    setup_mcp_service(app)

    return app


def setup_mcp_service(app: FastAPI):
    """设置MCP服务 - 使用官方FastApiMCP框架 + SSE连接保持"""
    try:
        logger.info("🚀 设置MCP服务...")

        # 使用缓存的自动发现标签
        _, auto_tags = get_cached_modules_and_tags()

        logger.info(f"🏷️ 使用自动发现的标签: {auto_tags}")

        # 使用官方FastApiMCP框架，暴露所有功能域
        mcp = FastApiMCP(
            app,
            include_tags=auto_tags
        )

        # 挂载MCP服务
        mcp.mount()

        logger.info("✅ MCP服务设置完成 - 使用官方FastApiMCP框架")
        logger.info(f"🔌 MCP端点: http://localhost:{SERVER_PORT}/mcp")
        return mcp

    except Exception as e:
        logger.error(f"❌ MCP服务设置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


app = create_app()


def main():
    """主函数"""

    try:
        logger.info("🚀 启动esign-qa-mcp-platform服务")
        logger.info(f"📚 API文档: http://localhost:{SERVER_PORT}/docs")
        logger.info(f"🔌 MCP端点: http://localhost:{SERVER_PORT}/mcp")

        # 连接保持优化启动配置
        logger.info(f"🔧 启动参数: host={SERVER_HOST}, port={SERVER_PORT}")
        logger.info(f"🔧 连接保持: keep_alive={KEEP_ALIVE_TIMEOUT}秒, graceful_shutdown={GRACEFUL_SHUTDOWN_TIMEOUT}秒")

        # 使用uvicorn启动，配置连接保持参数
        uvicorn.run(
            "main:app",
            host=SERVER_HOST,
            port=SERVER_PORT,
            log_level=LOG_LEVEL.lower(),
            # 连接保持优化配置
            timeout_keep_alive=KEEP_ALIVE_TIMEOUT,
            timeout_graceful_shutdown=GRACEFUL_SHUTDOWN_TIMEOUT,
            access_log=True,
            # 启用WebSocket支持（FastMCP可能需要）
            ws_ping_interval=20,
            ws_ping_timeout=20,
            reload=True
        )

    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
